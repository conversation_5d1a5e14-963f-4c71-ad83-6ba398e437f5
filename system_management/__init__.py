"""
系统管理层
提供配置管理、日志管理、性能监控、数据备份、用户权限管理等功能
"""

from .config_manager.config_manager import ConfigManager
from .log_manager.log_manager import LogManager
from .performance_monitor.performance_monitor import PerformanceMonitor
from .user_manager.user_manager import UserManager
from .system_integration import SystemManagementIntegration

__version__ = "2.0.0"

__all__ = [
    'ConfigManager',
    'LogManager',
    'PerformanceMonitor',
    'UserManager',
    'SystemManagementIntegration'
]
