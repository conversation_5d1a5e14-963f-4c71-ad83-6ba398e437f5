"""
系统管理集成模块
整合所有系统管理组件，提供统一的接口
"""

import logging
import json
import os
import psutil
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, date
from pathlib import Path

logger = logging.getLogger(__name__)

class SystemManagementIntegration:
    """系统管理集成类"""
    
    def __init__(self):
        """初始化系统管理"""
        self.system_config = {}
        self.performance_metrics = {}
        self.log_handlers = {}
        self.backup_configs = {}
        
        # 系统路径
        self.config_dir = Path("config")
        self.log_dir = Path("logs")
        self.backup_dir = Path("backups")
        self.data_dir = Path("data")
        
        # 创建必要目录
        self._create_directories()
        
        # 初始化日志系统
        self._setup_logging()
        
        # 加载系统配置
        self._load_system_config()
        
        logger.info("⚙️ 系统管理集成模块初始化完成")
    
    def _create_directories(self):
        """创建必要的目录"""
        try:
            directories = [self.config_dir, self.log_dir, self.backup_dir, self.data_dir]
            for directory in directories:
                directory.mkdir(exist_ok=True)
            logger.info("📁 系统目录创建完成")
        except Exception as e:
            logger.error(f"❌ 创建系统目录失败: {e}")
    
    def _setup_logging(self):
        """设置日志系统"""
        try:
            # 配置日志格式
            log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            
            # 创建不同类型的日志处理器
            handlers = {
                'system': logging.FileHandler(self.log_dir / 'system.log'),
                'trading': logging.FileHandler(self.log_dir / 'trading.log'),
                'error': logging.FileHandler(self.log_dir / 'error.log'),
                'performance': logging.FileHandler(self.log_dir / 'performance.log')
            }
            
            for name, handler in handlers.items():
                handler.setFormatter(logging.Formatter(log_format))
                self.log_handlers[name] = handler
            
            # 设置根日志级别
            logging.getLogger().setLevel(logging.INFO)
            
            logger.info("📝 日志系统设置完成")
            
        except Exception as e:
            print(f"❌ 设置日志系统失败: {e}")
    
    def _load_system_config(self):
        """加载系统配置"""
        try:
            config_file = self.config_dir / 'system_config.json'
            
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    self.system_config = json.load(f)
            else:
                # 创建默认配置
                self.system_config = self._get_default_config()
                self.save_system_config()
            
            logger.info("⚙️ 系统配置加载完成")
            
        except Exception as e:
            logger.error(f"❌ 加载系统配置失败: {e}")
            self.system_config = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认系统配置"""
        return {
            'system': {
                'name': 'Quantitative Trading System',
                'version': '2.0.0',
                'environment': 'development',
                'timezone': 'Asia/Shanghai'
            },
            'database': {
                'host': 'localhost',
                'port': 3306,
                'database': 'quantitative_trading_system',
                'pool_size': 10,
                'pool_timeout': 30
            },
            'data_collection': {
                'interval': 300,
                'max_retries': 3,
                'timeout': 30,
                'batch_size': 100
            },
            'trading': {
                'commission_rate': 0.0003,
                'slippage_rate': 0.001,
                'max_position_weight': 0.10,
                'max_total_position': 0.80
            },
            'risk_management': {
                'max_drawdown': 0.15,
                'var_confidence': 0.95,
                'stress_test_enabled': True
            },
            'performance': {
                'monitoring_enabled': True,
                'alert_thresholds': {
                    'cpu_usage': 80,
                    'memory_usage': 80,
                    'disk_usage': 90
                }
            },
            'backup': {
                'enabled': True,
                'frequency': 'daily',
                'retention_days': 30,
                'compress': True
            }
        }
    
    def save_system_config(self) -> bool:
        """保存系统配置"""
        try:
            config_file = self.config_dir / 'system_config.json'
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.system_config, f, indent=2, ensure_ascii=False)
            
            logger.info("💾 系统配置已保存")
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存系统配置失败: {e}")
            return False
    
    def get_config(self, key_path: str, default=None) -> Any:
        """获取配置值"""
        try:
            keys = key_path.split('.')
            value = self.system_config
            
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default
            
            return value
            
        except Exception as e:
            logger.error(f"❌ 获取配置失败: {key_path} - {e}")
            return default
    
    def set_config(self, key_path: str, value: Any) -> bool:
        """设置配置值"""
        try:
            keys = key_path.split('.')
            config = self.system_config
            
            # 导航到目标位置
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            
            # 设置值
            config[keys[-1]] = value
            
            # 保存配置
            return self.save_system_config()
            
        except Exception as e:
            logger.error(f"❌ 设置配置失败: {key_path} - {e}")
            return False
    
    def collect_performance_metrics(self) -> Dict[str, Any]:
        """收集性能指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available = memory.available
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # 网络统计
            network = psutil.net_io_counters()
            
            # 进程信息
            process = psutil.Process()
            process_memory = process.memory_info()
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'cpu': {
                    'usage_percent': cpu_percent,
                    'count': psutil.cpu_count()
                },
                'memory': {
                    'usage_percent': memory_percent,
                    'available_gb': memory_available / (1024**3),
                    'total_gb': memory.total / (1024**3)
                },
                'disk': {
                    'usage_percent': disk_percent,
                    'free_gb': disk.free / (1024**3),
                    'total_gb': disk.total / (1024**3)
                },
                'network': {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                },
                'process': {
                    'memory_mb': process_memory.rss / (1024**2),
                    'cpu_percent': process.cpu_percent()
                }
            }
            
            self.performance_metrics = metrics
            
            # 检查告警阈值
            self._check_performance_alerts(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"❌ 收集性能指标失败: {e}")
            return {}
    
    def _check_performance_alerts(self, metrics: Dict[str, Any]):
        """检查性能告警"""
        try:
            thresholds = self.get_config('performance.alert_thresholds', {})
            
            # CPU告警
            cpu_threshold = thresholds.get('cpu_usage', 80)
            if metrics['cpu']['usage_percent'] > cpu_threshold:
                logger.warning(f"⚠️ CPU使用率告警: {metrics['cpu']['usage_percent']:.1f}% > {cpu_threshold}%")
            
            # 内存告警
            memory_threshold = thresholds.get('memory_usage', 80)
            if metrics['memory']['usage_percent'] > memory_threshold:
                logger.warning(f"⚠️ 内存使用率告警: {metrics['memory']['usage_percent']:.1f}% > {memory_threshold}%")
            
            # 磁盘告警
            disk_threshold = thresholds.get('disk_usage', 90)
            if metrics['disk']['usage_percent'] > disk_threshold:
                logger.warning(f"⚠️ 磁盘使用率告警: {metrics['disk']['usage_percent']:.1f}% > {disk_threshold}%")
                
        except Exception as e:
            logger.error(f"❌ 检查性能告警失败: {e}")
    
    def create_backup(self, backup_type: str = 'full') -> bool:
        """创建系统备份"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"{backup_type}_backup_{timestamp}"
            backup_path = self.backup_dir / backup_name
            
            logger.info(f"💾 开始创建备份: {backup_name}")
            
            # 创建备份目录
            backup_path.mkdir(exist_ok=True)
            
            # 备份配置文件
            config_backup = backup_path / 'config'
            config_backup.mkdir(exist_ok=True)
            
            if self.config_dir.exists():
                import shutil
                shutil.copytree(self.config_dir, config_backup, dirs_exist_ok=True)
            
            # 备份日志文件（最近7天）
            log_backup = backup_path / 'logs'
            log_backup.mkdir(exist_ok=True)
            
            # 创建备份信息文件
            backup_info = {
                'backup_type': backup_type,
                'timestamp': timestamp,
                'system_version': self.get_config('system.version'),
                'files_count': len(list(backup_path.rglob('*'))),
                'size_mb': sum(f.stat().st_size for f in backup_path.rglob('*') if f.is_file()) / (1024**2)
            }
            
            with open(backup_path / 'backup_info.json', 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 备份创建完成: {backup_name}")
            logger.info(f"  文件数量: {backup_info['files_count']}")
            logger.info(f"  备份大小: {backup_info['size_mb']:.2f} MB")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建备份失败: {e}")
            return False
    
    def cleanup_old_backups(self, retention_days: int = None) -> int:
        """清理旧备份"""
        try:
            if retention_days is None:
                retention_days = self.get_config('backup.retention_days', 30)
            
            cutoff_time = time.time() - (retention_days * 24 * 3600)
            deleted_count = 0
            
            for backup_dir in self.backup_dir.iterdir():
                if backup_dir.is_dir() and backup_dir.stat().st_mtime < cutoff_time:
                    import shutil
                    shutil.rmtree(backup_dir)
                    deleted_count += 1
                    logger.info(f"🗑️ 删除旧备份: {backup_dir.name}")
            
            logger.info(f"✅ 清理完成: 删除了 {deleted_count} 个旧备份")
            return deleted_count
            
        except Exception as e:
            logger.error(f"❌ 清理旧备份失败: {e}")
            return 0
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            # 收集最新性能指标
            metrics = self.collect_performance_metrics()
            
            # 统计备份信息
            backup_count = len([d for d in self.backup_dir.iterdir() if d.is_dir()])
            
            # 统计日志文件
            log_files = list(self.log_dir.glob('*.log'))
            log_size_mb = sum(f.stat().st_size for f in log_files) / (1024**2)
            
            status = {
                'system_info': {
                    'name': self.get_config('system.name'),
                    'version': self.get_config('system.version'),
                    'environment': self.get_config('system.environment'),
                    'uptime': time.time() - psutil.boot_time()
                },
                'performance': metrics,
                'storage': {
                    'config_files': len(list(self.config_dir.glob('*'))),
                    'log_files': len(log_files),
                    'log_size_mb': log_size_mb,
                    'backup_count': backup_count
                },
                'services': {
                    'logging': len(self.log_handlers) > 0,
                    'monitoring': self.get_config('performance.monitoring_enabled', False),
                    'backup': self.get_config('backup.enabled', False)
                },
                'last_update': datetime.now().isoformat()
            }
            
            return status
            
        except Exception as e:
            logger.error(f"❌ 获取系统状态失败: {e}")
            return {'error': str(e)}
    
    def restart_service(self, service_name: str) -> bool:
        """重启服务（模拟）"""
        try:
            logger.info(f"🔄 重启服务: {service_name}")
            
            # 这里应该实现具体的服务重启逻辑
            # 现在只是模拟
            time.sleep(1)
            
            logger.info(f"✅ 服务重启完成: {service_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 重启服务失败: {service_name} - {e}")
            return False
    
    def export_system_report(self, filename: str = None) -> str:
        """导出系统报告"""
        try:
            if filename is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"system_report_{timestamp}.json"
            
            report = {
                'report_info': {
                    'generated_at': datetime.now().isoformat(),
                    'report_type': 'system_status',
                    'version': '2.0.0'
                },
                'system_status': self.get_system_status(),
                'configuration': self.system_config,
                'performance_history': [self.performance_metrics] if self.performance_metrics else []
            }
            
            report_path = self.log_dir / filename
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"📄 系统报告已导出: {filename}")
            return str(report_path)
            
        except Exception as e:
            logger.error(f"❌ 导出系统报告失败: {e}")
            return ""
