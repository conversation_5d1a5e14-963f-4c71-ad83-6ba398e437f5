"""
可视化系统集成模块
整合所有可视化组件，提供统一的接口
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, date
import json
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd

logger = logging.getLogger(__name__)

class VisualizationSystem:
    """可视化系统集成类"""
    
    def __init__(self):
        """初始化可视化系统"""
        self.chart_configs = {
            'theme': 'plotly_dark',
            'color_palette': ['#00d4ff', '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'],
            'font_family': 'Arial, sans-serif',
            'font_size': 12
        }
        
        # 图表缓存
        self.chart_cache = {}
        
        logger.info("📊 可视化系统初始化完成")
    
    def create_kline_chart(self, 
                          symbol: str,
                          data: pd.DataFrame,
                          indicators: Dict[str, Any] = None,
                          signals: List[Dict[str, Any]] = None,
                          timeframe: str = "1d") -> Dict[str, Any]:
        """
        创建K线图表
        
        Args:
            symbol: 股票代码
            data: K线数据
            indicators: 技术指标数据
            signals: 交易信号
            timeframe: 时间周期
            
        Returns:
            图表配置
        """
        try:
            logger.info(f"📈 创建K线图表: {symbol} - {timeframe}")
            
            # 创建子图
            fig = make_subplots(
                rows=3, cols=1,
                shared_xaxes=True,
                vertical_spacing=0.03,
                subplot_titles=(f'{symbol} K线图', '成交量', '技术指标'),
                row_width=[0.2, 0.1, 0.1]
            )
            
            # K线图
            fig.add_trace(
                go.Candlestick(
                    x=data.index,
                    open=data['open'],
                    high=data['high'],
                    low=data['low'],
                    close=data['close'],
                    name='K线',
                    increasing_line_color='#00d4ff',
                    decreasing_line_color='#ff6b6b'
                ),
                row=1, col=1
            )
            
            # 成交量
            colors = ['#00d4ff' if close >= open else '#ff6b6b' 
                     for close, open in zip(data['close'], data['open'])]
            
            fig.add_trace(
                go.Bar(
                    x=data.index,
                    y=data['volume'],
                    name='成交量',
                    marker_color=colors,
                    opacity=0.7
                ),
                row=2, col=1
            )
            
            # 添加技术指标
            if indicators:
                for indicator_name, indicator_data in indicators.items():
                    if indicator_name in ['MA5', 'MA10', 'MA20', 'MA60']:
                        fig.add_trace(
                            go.Scatter(
                                x=data.index,
                                y=indicator_data,
                                name=indicator_name,
                                line=dict(width=1)
                            ),
                            row=1, col=1
                        )
                    elif indicator_name in ['RSI', 'MACD', 'KDJ']:
                        fig.add_trace(
                            go.Scatter(
                                x=data.index,
                                y=indicator_data,
                                name=indicator_name,
                                line=dict(width=1)
                            ),
                            row=3, col=1
                        )
            
            # 添加交易信号
            if signals:
                buy_signals = [s for s in signals if s['type'] == 'BUY']
                sell_signals = [s for s in signals if s['type'] == 'SELL']
                
                if buy_signals:
                    buy_x = [s['datetime'] for s in buy_signals]
                    buy_y = [s['price'] for s in buy_signals]
                    fig.add_trace(
                        go.Scatter(
                            x=buy_x,
                            y=buy_y,
                            mode='markers',
                            name='买入信号',
                            marker=dict(
                                symbol='triangle-up',
                                size=10,
                                color='#00ff00'
                            )
                        ),
                        row=1, col=1
                    )
                
                if sell_signals:
                    sell_x = [s['datetime'] for s in sell_signals]
                    sell_y = [s['price'] for s in sell_signals]
                    fig.add_trace(
                        go.Scatter(
                            x=sell_x,
                            y=sell_y,
                            mode='markers',
                            name='卖出信号',
                            marker=dict(
                                symbol='triangle-down',
                                size=10,
                                color='#ff0000'
                            )
                        ),
                        row=1, col=1
                    )
            
            # 更新布局
            fig.update_layout(
                title=f'{symbol} - {timeframe} K线图表',
                template=self.chart_configs['theme'],
                font=dict(
                    family=self.chart_configs['font_family'],
                    size=self.chart_configs['font_size']
                ),
                height=800,
                showlegend=True,
                xaxis_rangeslider_visible=False
            )
            
            # 更新x轴
            fig.update_xaxes(type='date')
            
            chart_config = {
                'figure': fig,
                'config': {
                    'displayModeBar': True,
                    'displaylogo': False,
                    'modeBarButtonsToRemove': ['pan2d', 'lasso2d']
                }
            }
            
            logger.info(f"✅ K线图表创建完成: {symbol}")
            return chart_config
            
        except Exception as e:
            logger.error(f"❌ 创建K线图表失败: {symbol} - {e}")
            return {}
    
    def create_performance_dashboard(self, 
                                   performance_data: Dict[str, Any],
                                   risk_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        创建绩效仪表板
        
        Args:
            performance_data: 绩效数据
            risk_data: 风险数据
            
        Returns:
            仪表板配置
        """
        try:
            logger.info("📊 创建绩效仪表板")
            
            # 创建子图
            fig = make_subplots(
                rows=2, cols=3,
                subplot_titles=(
                    '收益率分析', '风险指标', '交易统计',
                    '资金曲线', '回撤分析', '风险评级'
                ),
                specs=[
                    [{"type": "bar"}, {"type": "indicator"}, {"type": "pie"}],
                    [{"type": "scatter"}, {"type": "scatter"}, {"type": "indicator"}]
                ]
            )
            
            # 收益率分析
            returns_data = [
                performance_data.get('total_return', 0),
                performance_data.get('annual_return', 0),
                performance_data.get('max_drawdown', 0)
            ]
            returns_labels = ['总收益率', '年化收益率', '最大回撤']
            
            fig.add_trace(
                go.Bar(
                    x=returns_labels,
                    y=returns_data,
                    name='收益率',
                    marker_color=['#00d4ff', '#4ecdc4', '#ff6b6b']
                ),
                row=1, col=1
            )
            
            # 风险指标
            sharpe_ratio = performance_data.get('sharpe_ratio', 0)
            fig.add_trace(
                go.Indicator(
                    mode="gauge+number+delta",
                    value=sharpe_ratio,
                    domain={'x': [0, 1], 'y': [0, 1]},
                    title={'text': "夏普比率"},
                    gauge={
                        'axis': {'range': [None, 3]},
                        'bar': {'color': "#00d4ff"},
                        'steps': [
                            {'range': [0, 1], 'color': "#ff6b6b"},
                            {'range': [1, 2], 'color': "#feca57"},
                            {'range': [2, 3], 'color': "#4ecdc4"}
                        ],
                        'threshold': {
                            'line': {'color': "red", 'width': 4},
                            'thickness': 0.75,
                            'value': 2
                        }
                    }
                ),
                row=1, col=2
            )
            
            # 交易统计
            if 'winning_trades' in performance_data and 'losing_trades' in performance_data:
                trade_labels = ['盈利交易', '亏损交易']
                trade_values = [
                    performance_data.get('winning_trades', 0),
                    performance_data.get('losing_trades', 0)
                ]
                
                fig.add_trace(
                    go.Pie(
                        labels=trade_labels,
                        values=trade_values,
                        name="交易统计",
                        marker_colors=['#4ecdc4', '#ff6b6b']
                    ),
                    row=1, col=3
                )
            
            # 资金曲线（模拟数据）
            if 'daily_returns' in performance_data:
                daily_returns = performance_data['daily_returns']
                cumulative_returns = [1]
                for ret in daily_returns:
                    cumulative_returns.append(cumulative_returns[-1] * (1 + ret))
                
                fig.add_trace(
                    go.Scatter(
                        y=cumulative_returns,
                        mode='lines',
                        name='资金曲线',
                        line=dict(color='#00d4ff', width=2)
                    ),
                    row=2, col=1
                )
            
            # 风险评级
            if risk_data:
                risk_score = risk_data.get('risk_rating', {}).get('overall_score', 50)
                fig.add_trace(
                    go.Indicator(
                        mode="gauge+number",
                        value=risk_score,
                        domain={'x': [0, 1], 'y': [0, 1]},
                        title={'text': "风险评分"},
                        gauge={
                            'axis': {'range': [0, 100]},
                            'bar': {'color': "#00d4ff"},
                            'steps': [
                                {'range': [0, 40], 'color': "#ff6b6b"},
                                {'range': [40, 70], 'color': "#feca57"},
                                {'range': [70, 100], 'color': "#4ecdc4"}
                            ]
                        }
                    ),
                    row=2, col=3
                )
            
            # 更新布局
            fig.update_layout(
                title="投资组合绩效仪表板",
                template=self.chart_configs['theme'],
                font=dict(
                    family=self.chart_configs['font_family'],
                    size=self.chart_configs['font_size']
                ),
                height=800,
                showlegend=False
            )
            
            dashboard_config = {
                'figure': fig,
                'config': {
                    'displayModeBar': True,
                    'displaylogo': False
                }
            }
            
            logger.info("✅ 绩效仪表板创建完成")
            return dashboard_config
            
        except Exception as e:
            logger.error(f"❌ 创建绩效仪表板失败: {e}")
            return {}
    
    def create_factor_analysis_chart(self, 
                                   factor_data: Dict[str, Any],
                                   factor_performance: Dict[str, float] = None) -> Dict[str, Any]:
        """
        创建因子分析图表
        
        Args:
            factor_data: 因子数据
            factor_performance: 因子绩效
            
        Returns:
            图表配置
        """
        try:
            logger.info("🔧 创建因子分析图表")
            
            # 创建子图
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('因子权重分布', '因子绩效排名', '因子相关性', '因子稳定性'),
                specs=[
                    [{"type": "pie"}, {"type": "bar"}],
                    [{"type": "heatmap"}, {"type": "scatter"}]
                ]
            )
            
            # 因子权重分布
            factor_names = list(factor_data.keys())
            factor_weights = [factor_data[name].get('weight', 0) for name in factor_names]
            
            fig.add_trace(
                go.Pie(
                    labels=factor_names,
                    values=factor_weights,
                    name="因子权重",
                    marker_colors=self.chart_configs['color_palette']
                ),
                row=1, col=1
            )
            
            # 因子绩效排名
            if factor_performance:
                performance_names = list(factor_performance.keys())
                performance_values = list(factor_performance.values())
                
                fig.add_trace(
                    go.Bar(
                        x=performance_names,
                        y=performance_values,
                        name='因子绩效',
                        marker_color=self.chart_configs['color_palette'][0]
                    ),
                    row=1, col=2
                )
            
            # 更新布局
            fig.update_layout(
                title="因子分析图表",
                template=self.chart_configs['theme'],
                font=dict(
                    family=self.chart_configs['font_family'],
                    size=self.chart_configs['font_size']
                ),
                height=800,
                showlegend=False
            )
            
            chart_config = {
                'figure': fig,
                'config': {
                    'displayModeBar': True,
                    'displaylogo': False
                }
            }
            
            logger.info("✅ 因子分析图表创建完成")
            return chart_config
            
        except Exception as e:
            logger.error(f"❌ 创建因子分析图表失败: {e}")
            return {}
    
    def export_chart(self, chart_config: Dict[str, Any], filename: str, format: str = "html") -> bool:
        """导出图表"""
        try:
            fig = chart_config.get('figure')
            if not fig:
                logger.error("❌ 图表配置无效")
                return False
            
            if format.lower() == "html":
                fig.write_html(f"{filename}.html")
            elif format.lower() == "png":
                fig.write_image(f"{filename}.png")
            elif format.lower() == "pdf":
                fig.write_image(f"{filename}.pdf")
            else:
                logger.error(f"❌ 不支持的格式: {format}")
                return False
            
            logger.info(f"✅ 图表已导出: {filename}.{format}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 导出图表失败: {e}")
            return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'chart_cache_size': len(self.chart_cache),
            'theme': self.chart_configs['theme'],
            'available_formats': ['html', 'png', 'pdf'],
            'components': {
                'plotly': True,
                'pandas': True
            }
        }
