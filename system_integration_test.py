#!/usr/bin/env python3
"""
系统集成测试
验证所有模块的集成和功能完整性
"""

import sys
import logging
import unittest
from pathlib import Path
from datetime import datetime, date, timedelta

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

# 设置测试日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class SystemIntegrationTest(unittest.TestCase):
    """系统集成测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        logger.info("🧪 开始系统集成测试")
        cls.test_results = {}
    
    def test_01_database_models_import(self):
        """测试数据库模型导入"""
        try:
            from database_models import (
                StockInfo, BaseMarketData, FactorConfig,
                BacktestResult, PortfolioHolding, TradingSignal
            )
            self.test_results['database_models'] = True
            logger.info("✅ 数据库模型导入测试通过")
        except Exception as e:
            self.test_results['database_models'] = False
            logger.error(f"❌ 数据库模型导入测试失败: {e}")
            self.fail(f"数据库模型导入失败: {e}")
    
    def test_02_vnpy_backtesting_system(self):
        """测试VeighNa回测系统"""
        try:
            from vnpy_backtesting import VeighNaBacktestingSystem
            
            # 创建回测系统
            vnpy_system = VeighNaBacktestingSystem()
            self.assertIsNotNone(vnpy_system)
            
            # 检查系统状态
            status = vnpy_system.get_system_status()
            self.assertIsInstance(status, dict)
            self.assertIn('components', status)
            
            self.test_results['vnpy_backtesting'] = True
            logger.info("✅ VeighNa回测系统测试通过")
            
        except Exception as e:
            self.test_results['vnpy_backtesting'] = False
            logger.error(f"❌ VeighNa回测系统测试失败: {e}")
            self.fail(f"VeighNa回测系统测试失败: {e}")
    
    def test_03_factor_management_system(self):
        """测试因子管理系统"""
        try:
            from factor_management import FactorManagementSystem
            
            # 创建因子管理系统
            factor_system = FactorManagementSystem()
            self.assertIsNotNone(factor_system)
            
            # 初始化系统
            init_result = factor_system.initialize_system()
            self.assertTrue(init_result)
            
            # 获取所有因子
            factors = factor_system.get_all_factors()
            self.assertIsInstance(factors, dict)
            
            # 检查系统状态
            status = factor_system.get_system_status()
            self.assertIsInstance(status, dict)
            
            self.test_results['factor_management'] = True
            logger.info("✅ 因子管理系统测试通过")
            
        except Exception as e:
            self.test_results['factor_management'] = False
            logger.error(f"❌ 因子管理系统测试失败: {e}")
            self.fail(f"因子管理系统测试失败: {e}")
    
    def test_04_portfolio_management_system(self):
        """测试投资组合管理系统"""
        try:
            from portfolio_management import PortfolioManagementSystem, WeightMethod
            
            # 创建投资组合管理系统
            portfolio_system = PortfolioManagementSystem()
            self.assertIsNotNone(portfolio_system)
            
            # 模拟数据
            stock_scores = {
                '000001.SZ': 85.5,
                '000002.SZ': 78.2,
                '600000.SH': 82.1
            }
            
            stock_data = {}
            for symbol in stock_scores:
                stock_data[symbol] = {
                    'market_cap': 50e9,
                    'avg_volume': 5e6,
                    'price': 15.0,
                    'sector': 'Finance',
                    'score': stock_scores[symbol],
                    'volatility': 0.25,
                    'expected_return': 0.12
                }
            
            # 构建投资组合
            portfolio = portfolio_system.construct_portfolio(
                stock_scores=stock_scores,
                stock_data=stock_data,
                weight_method=WeightMethod.EQUAL_WEIGHT,
                target_stocks=3
            )
            
            self.assertIsInstance(portfolio, dict)
            self.assertGreater(len(portfolio), 0)
            
            # 检查权重总和
            total_weight = sum(portfolio.values())
            self.assertAlmostEqual(total_weight, 1.0, places=2)
            
            self.test_results['portfolio_management'] = True
            logger.info("✅ 投资组合管理系统测试通过")
            
        except Exception as e:
            self.test_results['portfolio_management'] = False
            logger.error(f"❌ 投资组合管理系统测试失败: {e}")
            self.fail(f"投资组合管理系统测试失败: {e}")
    
    def test_05_trading_execution_system(self):
        """测试交易执行系统"""
        try:
            from trading_execution import TradingExecutionSystem, OrderType
            
            # 创建交易执行系统
            trading_system = TradingExecutionSystem()
            self.assertIsNotNone(trading_system)
            
            # 启动交易系统
            start_result = trading_system.start_trading()
            self.assertTrue(start_result)
            
            # 提交测试订单
            order_id = trading_system.submit_order(
                symbol='000001.SZ',
                order_type=OrderType.BUY,
                quantity=100,
                reason='集成测试'
            )
            
            self.assertIsNotNone(order_id)
            
            # 获取投资组合状态
            status = trading_system.get_portfolio_status()
            self.assertIsInstance(status, dict)
            self.assertIn('current_value', status)
            
            # 停止交易系统
            stop_result = trading_system.stop_trading()
            self.assertTrue(stop_result)
            
            self.test_results['trading_execution'] = True
            logger.info("✅ 交易执行系统测试通过")
            
        except Exception as e:
            self.test_results['trading_execution'] = False
            logger.error(f"❌ 交易执行系统测试失败: {e}")
            self.fail(f"交易执行系统测试失败: {e}")
    
    def test_06_visualization_system(self):
        """测试可视化系统"""
        try:
            from visualization import VisualizationSystem
            import pandas as pd
            
            # 创建可视化系统
            viz_system = VisualizationSystem()
            self.assertIsNotNone(viz_system)
            
            # 创建测试数据
            dates = pd.date_range('2023-01-01', periods=100, freq='D')
            test_data = pd.DataFrame({
                'open': [10.0 + i * 0.1 for i in range(100)],
                'high': [10.5 + i * 0.1 for i in range(100)],
                'low': [9.5 + i * 0.1 for i in range(100)],
                'close': [10.2 + i * 0.1 for i in range(100)],
                'volume': [1000000 + i * 10000 for i in range(100)]
            }, index=dates)
            
            # 创建K线图表
            chart_config = viz_system.create_kline_chart(
                symbol='TEST001',
                data=test_data
            )
            
            self.assertIsInstance(chart_config, dict)
            
            # 创建绩效仪表板
            performance_data = {
                'total_return': 0.15,
                'annual_return': 0.12,
                'max_drawdown': -0.08,
                'sharpe_ratio': 1.5,
                'winning_trades': 60,
                'losing_trades': 40
            }
            
            dashboard_config = viz_system.create_performance_dashboard(performance_data)
            self.assertIsInstance(dashboard_config, dict)
            
            self.test_results['visualization'] = True
            logger.info("✅ 可视化系统测试通过")
            
        except Exception as e:
            self.test_results['visualization'] = False
            logger.error(f"❌ 可视化系统测试失败: {e}")
            self.fail(f"可视化系统测试失败: {e}")
    
    def test_07_system_management(self):
        """测试系统管理"""
        try:
            from system_management import SystemManagementIntegration
            
            # 创建系统管理
            sys_mgmt = SystemManagementIntegration()
            self.assertIsNotNone(sys_mgmt)
            
            # 获取系统状态
            status = sys_mgmt.get_system_status()
            self.assertIsInstance(status, dict)
            self.assertIn('system_info', status)
            
            # 测试配置管理
            test_key = 'test.integration.value'
            test_value = 'integration_test_value'
            
            set_result = sys_mgmt.set_config(test_key, test_value)
            self.assertTrue(set_result)
            
            get_value = sys_mgmt.get_config(test_key)
            self.assertEqual(get_value, test_value)
            
            self.test_results['system_management'] = True
            logger.info("✅ 系统管理测试通过")
            
        except Exception as e:
            self.test_results['system_management'] = False
            logger.error(f"❌ 系统管理测试失败: {e}")
            self.fail(f"系统管理测试失败: {e}")
    
    def test_08_system_launcher(self):
        """测试系统启动器"""
        try:
            from system_launcher import QuantitativeTradingSystemLauncher
            
            # 创建系统启动器
            launcher = QuantitativeTradingSystemLauncher()
            self.assertIsNotNone(launcher)
            
            # 初始化系统
            init_result = launcher.initialize_systems()
            self.assertTrue(init_result)
            
            # 获取系统状态
            status = launcher.get_system_status()
            self.assertIsInstance(status, dict)
            self.assertIn('launcher', status)
            self.assertIn('systems', status)
            
            # 关闭系统
            launcher.shutdown()
            
            self.test_results['system_launcher'] = True
            logger.info("✅ 系统启动器测试通过")
            
        except Exception as e:
            self.test_results['system_launcher'] = False
            logger.error(f"❌ 系统启动器测试失败: {e}")
            self.fail(f"系统启动器测试失败: {e}")
    
    def test_09_data_flow_integration(self):
        """测试数据流集成"""
        try:
            # 测试完整的数据流：因子计算 -> 选股 -> 组合构建 -> 交易执行
            from factor_management import FactorManagementSystem
            from portfolio_management import PortfolioManagementSystem, WeightMethod
            from trading_execution import TradingExecutionSystem, OrderType
            
            # 1. 因子计算
            factor_system = FactorManagementSystem()
            factor_system.initialize_system()
            
            symbols = ['000001.SZ', '000002.SZ', '600000.SH']
            factor_values = factor_system.calculate_factors(symbols)
            self.assertIsInstance(factor_values, dict)
            
            # 2. 模拟选股评分
            stock_scores = {}
            for symbol in symbols:
                if symbol in factor_values:
                    # 简单的评分计算
                    score = sum(factor_values[symbol].values()) / len(factor_values[symbol])
                    stock_scores[symbol] = max(0, min(100, score * 100))
                else:
                    stock_scores[symbol] = 50.0
            
            # 3. 组合构建
            portfolio_system = PortfolioManagementSystem()
            stock_data = {}
            for symbol in symbols:
                stock_data[symbol] = {
                    'market_cap': 50e9,
                    'avg_volume': 5e6,
                    'price': 15.0,
                    'sector': 'Finance',
                    'score': stock_scores[symbol],
                    'volatility': 0.25,
                    'expected_return': 0.12
                }
            
            portfolio = portfolio_system.construct_portfolio(
                stock_scores=stock_scores,
                stock_data=stock_data,
                weight_method=WeightMethod.SCORE_BASED,
                target_stocks=len(symbols)
            )
            
            self.assertIsInstance(portfolio, dict)
            self.assertGreater(len(portfolio), 0)
            
            # 4. 交易执行
            trading_system = TradingExecutionSystem()
            trading_system.start_trading()
            
            # 根据组合权重提交订单
            orders_submitted = 0
            for symbol, weight in portfolio.items():
                if weight > 0.01:  # 权重大于1%才交易
                    # 调整数量以符合风险限制
                    quantity = min(100, int(weight * 100))  # 减少数量避免风险检查失败
                    order_id = trading_system.submit_order(
                        symbol=symbol,
                        order_type=OrderType.BUY,
                        quantity=quantity,
                        reason='数据流集成测试'
                    )
                    if order_id:
                        orders_submitted += 1
            
            self.assertGreater(orders_submitted, 0)
            
            trading_system.stop_trading()
            
            self.test_results['data_flow_integration'] = True
            logger.info("✅ 数据流集成测试通过")
            
        except Exception as e:
            self.test_results['data_flow_integration'] = False
            logger.error(f"❌ 数据流集成测试失败: {e}")
            self.fail(f"数据流集成测试失败: {e}")
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        logger.info("🏁 系统集成测试完成")
        
        # 生成测试报告
        cls._generate_test_report()
    
    @classmethod
    def _generate_test_report(cls):
        """生成测试报告"""
        try:
            total_tests = len(cls.test_results)
            passed_tests = sum(1 for result in cls.test_results.values() if result)
            failed_tests = total_tests - passed_tests
            
            success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
            
            report = f"""
{'='*60}
系统集成测试报告
{'='*60}
测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
总测试数: {total_tests}
通过测试: {passed_tests}
失败测试: {failed_tests}
成功率: {success_rate:.1f}%

详细结果:
{'-'*60}
"""
            
            for test_name, result in cls.test_results.items():
                status = "✅ 通过" if result else "❌ 失败"
                report += f"{test_name:<30} {status}\n"
            
            report += f"\n{'='*60}\n"
            
            # 保存报告到文件
            report_file = Path('logs') / f'integration_test_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
            report_file.parent.mkdir(exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            print(report)
            logger.info(f"📄 测试报告已保存: {report_file}")
            
        except Exception as e:
            logger.error(f"❌ 生成测试报告失败: {e}")

def main():
    """主函数"""
    # 创建日志目录
    Path('logs').mkdir(exist_ok=True)
    
    # 运行测试
    unittest.main(verbosity=2, exit=False)

if __name__ == '__main__':
    main()
