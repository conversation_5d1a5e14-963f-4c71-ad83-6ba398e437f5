{"system": {"name": "Quantitative Trading System", "version": "2.0.0", "environment": "development", "timezone": "Asia/Shanghai"}, "database": {"host": "localhost", "port": 3306, "database": "quantitative_trading_system", "pool_size": 10, "pool_timeout": 30}, "data_collection": {"interval": 300, "max_retries": 3, "timeout": 30, "batch_size": 100}, "trading": {"commission_rate": 0.0003, "slippage_rate": 0.001, "max_position_weight": 0.1, "max_total_position": 0.8}, "risk_management": {"max_drawdown": 0.15, "var_confidence": 0.95, "stress_test_enabled": true}, "performance": {"monitoring_enabled": true, "alert_thresholds": {"cpu_usage": 80, "memory_usage": 80, "disk_usage": 90}}, "backup": {"enabled": true, "frequency": "daily", "retention_days": 30, "compress": true}, "test": {"integration": {"value": "integration_test_value"}}}