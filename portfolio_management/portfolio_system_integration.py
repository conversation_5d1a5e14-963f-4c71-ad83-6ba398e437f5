"""
投资组合管理系统集成模块
整合所有投资组合管理组件，提供统一的接口
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, date, timedelta
import numpy as np
import pandas as pd
from enum import Enum

logger = logging.getLogger(__name__)

class WeightMethod(Enum):
    """权重分配方法"""
    EQUAL_WEIGHT = "EQUAL_WEIGHT"
    RISK_PARITY = "RISK_PARITY"
    OPTIMIZATION = "OPTIMIZATION"
    MARKET_CAP = "MARKET_CAP"
    SCORE_BASED = "SCORE_BASED"

class RebalanceMethod(Enum):
    """再平衡方法"""
    PERIODIC = "PERIODIC"
    THRESHOLD = "THRESHOLD"
    SIGNAL_DRIVEN = "SIGNAL_DRIVEN"
    VOLATILITY_BASED = "VOLATILITY_BASED"

class PortfolioManagementSystem:
    """投资组合管理系统集成类"""
    
    def __init__(self, initial_capital: float = 1000000.0):
        """初始化投资组合管理系统"""
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        
        # 组合配置
        self.max_stocks = 50
        self.min_weight = 0.01  # 最小权重1%
        self.max_weight = 0.10  # 最大权重10%
        self.rebalance_threshold = 0.05  # 再平衡阈值5%
        
        # 风险控制参数
        self.max_sector_weight = 0.30  # 单行业最大权重30%
        self.max_correlation = 0.80    # 最大相关性80%
        self.target_volatility = 0.15  # 目标波动率15%
        
        # 数据存储
        self.current_portfolio: Dict[str, float] = {}  # 当前组合权重
        self.target_portfolio: Dict[str, float] = {}   # 目标组合权重
        self.portfolio_history: List[Dict[str, Any]] = []
        self.rebalance_history: List[Dict[str, Any]] = []
        self.performance_history: List[Dict[str, Any]] = []
        
        # 系统状态
        self.last_rebalance_date = None
        self.next_rebalance_date = None
        
        logger.info("💼 投资组合管理系统初始化完成")
        logger.info(f"  初始资金: {initial_capital:,.0f}")
        logger.info(f"  最大股票数: {self.max_stocks}")
        logger.info(f"  权重范围: {self.min_weight:.1%} - {self.max_weight:.1%}")
    
    def construct_portfolio(self,
                          stock_scores: Dict[str, float],
                          stock_data: Dict[str, Dict[str, Any]],
                          weight_method: WeightMethod = WeightMethod.SCORE_BASED,
                          target_stocks: int = 20) -> Dict[str, float]:
        """
        构建投资组合
        
        Args:
            stock_scores: 股票评分
            stock_data: 股票数据
            weight_method: 权重分配方法
            target_stocks: 目标股票数量
            
        Returns:
            组合权重分配
        """
        try:
            logger.info(f"🏗️ 构建投资组合: {len(stock_scores)}只候选股票 -> {target_stocks}只目标股票")
            
            # 1. 股票筛选
            selected_stocks = self._select_stocks(stock_scores, stock_data, target_stocks)
            if not selected_stocks:
                logger.error("❌ 股票筛选失败")
                return {}
            
            logger.info(f"  筛选完成: {len(selected_stocks)}只股票")
            
            # 2. 权重分配
            weights = self._allocate_weights(selected_stocks, stock_data, weight_method)
            if not weights:
                logger.error("❌ 权重分配失败")
                return {}
            
            # 3. 风险检查
            if not self._risk_check_portfolio(weights, stock_data):
                logger.warning("⚠️ 组合风险检查未通过，调整权重")
                weights = self._adjust_weights_for_risk(weights, stock_data)
            
            # 4. 权重标准化
            weights = self._normalize_weights(weights)
            
            self.target_portfolio = weights
            
            logger.info("✅ 投资组合构建完成")
            self._log_portfolio_summary(weights)
            
            return weights
            
        except Exception as e:
            logger.error(f"❌ 构建投资组合失败: {e}")
            return {}
    
    def _select_stocks(self, 
                      stock_scores: Dict[str, float],
                      stock_data: Dict[str, Dict[str, Any]],
                      target_count: int) -> List[str]:
        """股票筛选"""
        try:
            # 按评分排序
            sorted_stocks = sorted(stock_scores.items(), key=lambda x: x[1], reverse=True)
            
            selected = []
            sector_counts = {}
            
            for symbol, score in sorted_stocks:
                if len(selected) >= target_count:
                    break
                
                # 基础过滤
                if not self._basic_filter(symbol, stock_data.get(symbol, {})):
                    continue
                
                # 行业分散检查
                sector = stock_data.get(symbol, {}).get('sector', 'Unknown')
                sector_count = sector_counts.get(sector, 0)
                max_sector_stocks = max(1, target_count // 5)  # 每个行业最多20%的股票
                
                if sector_count >= max_sector_stocks:
                    continue
                
                selected.append(symbol)
                sector_counts[sector] = sector_count + 1
            
            return selected
            
        except Exception as e:
            logger.error(f"❌ 股票筛选失败: {e}")
            return []
    
    def _basic_filter(self, symbol: str, stock_info: Dict[str, Any]) -> bool:
        """基础过滤条件"""
        try:
            # 市值过滤
            market_cap = stock_info.get('market_cap', 0)
            if market_cap < 1e9:  # 市值小于10亿
                return False
            
            # 流动性过滤
            avg_volume = stock_info.get('avg_volume', 0)
            if avg_volume < 1e6:  # 日均成交量小于100万
                return False
            
            # 价格过滤
            price = stock_info.get('price', 0)
            if price < 2.0 or price > 1000.0:  # 价格范围
                return False
            
            # ST股票过滤
            if 'ST' in symbol or '*ST' in symbol:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 基础过滤失败: {symbol} - {e}")
            return False
    
    def _allocate_weights(self,
                         selected_stocks: List[str],
                         stock_data: Dict[str, Dict[str, Any]],
                         method: WeightMethod) -> Dict[str, float]:
        """权重分配"""
        try:
            weights = {}
            
            if method == WeightMethod.EQUAL_WEIGHT:
                # 等权重
                weight = 1.0 / len(selected_stocks)
                weights = {symbol: weight for symbol in selected_stocks}
                
            elif method == WeightMethod.MARKET_CAP:
                # 市值加权
                market_caps = {}
                for symbol in selected_stocks:
                    market_caps[symbol] = stock_data.get(symbol, {}).get('market_cap', 1e9)
                
                total_market_cap = sum(market_caps.values())
                weights = {symbol: cap / total_market_cap for symbol, cap in market_caps.items()}
                
            elif method == WeightMethod.SCORE_BASED:
                # 评分加权
                scores = {}
                for symbol in selected_stocks:
                    scores[symbol] = stock_data.get(symbol, {}).get('score', 50)
                
                total_score = sum(scores.values())
                weights = {symbol: score / total_score for symbol, score in scores.items()}
                
            elif method == WeightMethod.RISK_PARITY:
                # 风险平价
                weights = self._risk_parity_weights(selected_stocks, stock_data)
                
            elif method == WeightMethod.OPTIMIZATION:
                # 优化权重
                weights = self._optimize_weights(selected_stocks, stock_data)
            
            return weights
            
        except Exception as e:
            logger.error(f"❌ 权重分配失败: {method} - {e}")
            return {}
    
    def _risk_parity_weights(self, stocks: List[str], stock_data: Dict[str, Dict[str, Any]]) -> Dict[str, float]:
        """风险平价权重计算"""
        try:
            # 获取波动率数据
            volatilities = {}
            for symbol in stocks:
                vol = stock_data.get(symbol, {}).get('volatility', 0.20)
                volatilities[symbol] = max(vol, 0.01)  # 避免除零
            
            # 计算风险平价权重
            inv_vol = {symbol: 1.0 / vol for symbol, vol in volatilities.items()}
            total_inv_vol = sum(inv_vol.values())
            
            weights = {symbol: inv_vol_val / total_inv_vol for symbol, inv_vol_val in inv_vol.items()}
            
            return weights
            
        except Exception as e:
            logger.error(f"❌ 风险平价权重计算失败: {e}")
            return {}
    
    def _optimize_weights(self, stocks: List[str], stock_data: Dict[str, Dict[str, Any]]) -> Dict[str, float]:
        """优化权重计算（简化版）"""
        try:
            # 这里实现一个简化的均值方差优化
            n = len(stocks)
            
            # 获取预期收益和风险
            expected_returns = []
            volatilities = []
            
            for symbol in stocks:
                ret = stock_data.get(symbol, {}).get('expected_return', 0.10)
                vol = stock_data.get(symbol, {}).get('volatility', 0.20)
                expected_returns.append(ret)
                volatilities.append(vol)
            
            # 简化的优化：基于夏普比率
            sharpe_ratios = [ret / vol for ret, vol in zip(expected_returns, volatilities)]
            total_sharpe = sum(sharpe_ratios)
            
            weights = {}
            for i, symbol in enumerate(stocks):
                weights[symbol] = sharpe_ratios[i] / total_sharpe
            
            return weights
            
        except Exception as e:
            logger.error(f"❌ 优化权重计算失败: {e}")
            return {}
    
    def _risk_check_portfolio(self, weights: Dict[str, float], stock_data: Dict[str, Dict[str, Any]]) -> bool:
        """组合风险检查"""
        try:
            # 检查单股权重
            for symbol, weight in weights.items():
                if weight > self.max_weight:
                    logger.warning(f"⚠️ 单股权重超限: {symbol} {weight:.2%} > {self.max_weight:.2%}")
                    return False
            
            # 检查行业集中度
            sector_weights = {}
            for symbol, weight in weights.items():
                sector = stock_data.get(symbol, {}).get('sector', 'Unknown')
                sector_weights[sector] = sector_weights.get(sector, 0) + weight
            
            for sector, weight in sector_weights.items():
                if weight > self.max_sector_weight:
                    logger.warning(f"⚠️ 行业权重超限: {sector} {weight:.2%} > {self.max_sector_weight:.2%}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 组合风险检查失败: {e}")
            return False
    
    def _adjust_weights_for_risk(self, weights: Dict[str, float], stock_data: Dict[str, Dict[str, Any]]) -> Dict[str, float]:
        """调整权重以满足风险约束"""
        try:
            adjusted_weights = weights.copy()
            
            # 调整单股权重
            for symbol in adjusted_weights:
                if adjusted_weights[symbol] > self.max_weight:
                    adjusted_weights[symbol] = self.max_weight
            
            # 重新标准化
            adjusted_weights = self._normalize_weights(adjusted_weights)
            
            return adjusted_weights
            
        except Exception as e:
            logger.error(f"❌ 调整权重失败: {e}")
            return weights
    
    def _normalize_weights(self, weights: Dict[str, float]) -> Dict[str, float]:
        """权重标准化"""
        try:
            total_weight = sum(weights.values())
            if total_weight == 0:
                return weights
            
            normalized = {symbol: weight / total_weight for symbol, weight in weights.items()}
            
            # 过滤掉过小的权重
            filtered = {symbol: weight for symbol, weight in normalized.items() if weight >= self.min_weight}
            
            # 再次标准化
            total_filtered = sum(filtered.values())
            if total_filtered > 0:
                final_weights = {symbol: weight / total_filtered for symbol, weight in filtered.items()}
            else:
                final_weights = filtered
            
            return final_weights
            
        except Exception as e:
            logger.error(f"❌ 权重标准化失败: {e}")
            return weights
    
    def check_rebalance_need(self, 
                           current_weights: Dict[str, float],
                           method: RebalanceMethod = RebalanceMethod.THRESHOLD) -> bool:
        """检查是否需要再平衡"""
        try:
            if not self.target_portfolio:
                return False
            
            if method == RebalanceMethod.PERIODIC:
                # 定期再平衡
                if self.last_rebalance_date is None:
                    return True
                
                days_since_rebalance = (datetime.now().date() - self.last_rebalance_date).days
                return days_since_rebalance >= 30  # 30天再平衡
                
            elif method == RebalanceMethod.THRESHOLD:
                # 阈值触发再平衡
                max_deviation = 0
                for symbol in self.target_portfolio:
                    target_weight = self.target_portfolio[symbol]
                    current_weight = current_weights.get(symbol, 0)
                    deviation = abs(target_weight - current_weight)
                    max_deviation = max(max_deviation, deviation)
                
                return max_deviation > self.rebalance_threshold
            
            return False
            
        except Exception as e:
            logger.error(f"❌ 检查再平衡需求失败: {e}")
            return False
    
    def generate_rebalance_orders(self, 
                                current_weights: Dict[str, float],
                                current_prices: Dict[str, float]) -> List[Dict[str, Any]]:
        """生成再平衡订单"""
        try:
            orders = []
            
            if not self.target_portfolio:
                return orders
            
            logger.info("📋 生成再平衡订单")
            
            # 计算需要调整的股票
            all_symbols = set(list(current_weights.keys()) + list(self.target_portfolio.keys()))
            
            for symbol in all_symbols:
                current_weight = current_weights.get(symbol, 0)
                target_weight = self.target_portfolio.get(symbol, 0)
                
                weight_diff = target_weight - current_weight
                
                if abs(weight_diff) > 0.01:  # 权重差异大于1%
                    # 计算交易金额
                    trade_value = weight_diff * self.current_capital
                    
                    if symbol in current_prices:
                        price = current_prices[symbol]
                        quantity = int(trade_value / price / 100) * 100  # 整手交易
                        
                        if quantity != 0:
                            order_type = "BUY" if quantity > 0 else "SELL"
                            orders.append({
                                'symbol': symbol,
                                'order_type': order_type,
                                'quantity': abs(quantity),
                                'price': price,
                                'reason': f'再平衡: {current_weight:.2%} -> {target_weight:.2%}'
                            })
            
            logger.info(f"  生成订单数量: {len(orders)}")
            return orders
            
        except Exception as e:
            logger.error(f"❌ 生成再平衡订单失败: {e}")
            return []
    
    def calculate_portfolio_performance(self, 
                                     weights: Dict[str, float],
                                     returns: Dict[str, float]) -> Dict[str, Any]:
        """计算组合绩效"""
        try:
            # 组合收益率
            portfolio_return = sum(weights.get(symbol, 0) * ret for symbol, ret in returns.items())
            
            # 组合风险（简化计算）
            portfolio_risk = np.sqrt(sum((weights.get(symbol, 0) * 0.20) ** 2 for symbol in weights))
            
            # 夏普比率
            risk_free_rate = 0.03
            sharpe_ratio = (portfolio_return - risk_free_rate) / portfolio_risk if portfolio_risk > 0 else 0
            
            performance = {
                'portfolio_return': portfolio_return,
                'portfolio_risk': portfolio_risk,
                'sharpe_ratio': sharpe_ratio,
                'weights': weights,
                'calculation_date': datetime.now().isoformat()
            }
            
            self.performance_history.append(performance)
            
            return performance
            
        except Exception as e:
            logger.error(f"❌ 计算组合绩效失败: {e}")
            return {}
    
    def _log_portfolio_summary(self, weights: Dict[str, float]) -> None:
        """记录组合摘要"""
        try:
            logger.info("📊 投资组合摘要:")
            logger.info(f"  股票数量: {len(weights)}")
            logger.info(f"  权重范围: {min(weights.values()):.2%} - {max(weights.values()):.2%}")
            logger.info(f"  权重总和: {sum(weights.values()):.2%}")
            
            # 显示前5大持仓
            top_holdings = sorted(weights.items(), key=lambda x: x[1], reverse=True)[:5]
            logger.info("  前5大持仓:")
            for symbol, weight in top_holdings:
                logger.info(f"    {symbol}: {weight:.2%}")
                
        except Exception as e:
            logger.error(f"❌ 记录组合摘要失败: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'initial_capital': self.initial_capital,
            'current_capital': self.current_capital,
            'current_portfolio_size': len(self.current_portfolio),
            'target_portfolio_size': len(self.target_portfolio),
            'last_rebalance_date': self.last_rebalance_date.isoformat() if self.last_rebalance_date else None,
            'rebalance_count': len(self.rebalance_history),
            'performance_records': len(self.performance_history),
            'risk_limits': {
                'max_weight': self.max_weight,
                'max_sector_weight': self.max_sector_weight,
                'rebalance_threshold': self.rebalance_threshold
            }
        }
