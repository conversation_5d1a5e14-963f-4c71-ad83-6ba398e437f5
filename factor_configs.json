{"tech_rsi": {"factor_id": "tech_rsi", "factor_name": "RSI", "factor_name_cn": "相对强弱指标", "category": "TECHNICAL", "factor_type": "MOMENTUM", "description": "相对强弱指标，衡量价格动量", "calculation_method": "rsi", "parameters": {"period": 14, "overbought": 70, "oversold": 30}, "weight": 0.15, "is_enabled": true, "min_value": 0, "max_value": 100, "normalization_method": "z_score", "update_frequency": "daily", "data_requirements": ["close_price"], "created_time": "2025-07-29T11:11:02.719030", "updated_time": "2025-07-29T11:11:02.719037"}, "tech_macd": {"factor_id": "tech_macd", "factor_name": "MACD", "factor_name_cn": "指数平滑移动平均线", "category": "TECHNICAL", "factor_type": "MOMENTUM", "description": "MACD指标，衡量趋势变化", "calculation_method": "macd", "parameters": {"fast_period": 12, "slow_period": 26, "signal_period": 9}, "weight": 0.12, "is_enabled": true, "min_value": null, "max_value": null, "normalization_method": "z_score", "update_frequency": "daily", "data_requirements": ["close_price"], "created_time": "2025-07-29T11:11:02.719039", "updated_time": "2025-07-29T11:11:02.719039"}, "tech_bollinger": {"factor_id": "tech_bollinger", "factor_name": "Bollinger_Bands", "factor_name_cn": "布林带", "category": "TECHNICAL", "factor_type": "REVERSAL", "description": "布林带指标，衡量价格相对位置", "calculation_method": "bollinger_bands", "parameters": {"period": 20, "std_dev": 2}, "weight": 0.1, "is_enabled": true, "min_value": null, "max_value": null, "normalization_method": "z_score", "update_frequency": "daily", "data_requirements": ["close_price"], "created_time": "2025-07-29T11:11:02.719040", "updated_time": "2025-07-29T11:11:02.719041"}, "tech_kdj": {"factor_id": "tech_kdj", "factor_name": "KDJ", "factor_name_cn": "随机指标", "category": "TECHNICAL", "factor_type": "MOMENTUM", "description": "KDJ随机指标，衡量超买超卖", "calculation_method": "kdj", "parameters": {"k_period": 9, "d_period": 3, "j_period": 3}, "weight": 0.08, "is_enabled": true, "min_value": null, "max_value": null, "normalization_method": "z_score", "update_frequency": "daily", "data_requirements": ["high_price", "low_price", "close_price"], "created_time": "2025-07-29T11:11:02.719041", "updated_time": "2025-07-29T11:11:02.719042"}, "tech_ma_trend": {"factor_id": "tech_ma_trend", "factor_name": "MA_Trend", "factor_name_cn": "均线趋势", "category": "TECHNICAL", "factor_type": "MOMENTUM", "description": "多周期均线趋势强度", "calculation_method": "ma_trend", "parameters": {"periods": [5, 10, 20, 60], "trend_threshold": 0.02}, "weight": 0.05, "is_enabled": true, "min_value": null, "max_value": null, "normalization_method": "z_score", "update_frequency": "daily", "data_requirements": ["close_price"], "created_time": "2025-07-29T11:11:02.719043", "updated_time": "2025-07-29T11:11:02.719043"}, "fund_roe": {"factor_id": "fund_roe", "factor_name": "ROE", "factor_name_cn": "净资产收益率", "category": "FUNDAMENTAL", "factor_type": "QUALITY", "description": "净资产收益率，衡量盈利能力", "calculation_method": "roe", "parameters": {"period": "ttm", "min_roe": 0.05}, "weight": 0.12, "is_enabled": true, "min_value": -1, "max_value": 1, "normalization_method": "z_score", "update_frequency": "daily", "data_requirements": ["net_profit", "shareholders_equity"], "created_time": "2025-07-29T11:11:02.719044", "updated_time": "2025-07-29T11:11:02.719044"}, "fund_pe_ratio": {"factor_id": "fund_pe_ratio", "factor_name": "PE_Ratio", "factor_name_cn": "市盈率", "category": "FUNDAMENTAL", "factor_type": "VALUE", "description": "市盈率，衡量估值水平", "calculation_method": "pe_ratio", "parameters": {"period": "ttm", "max_pe": 100}, "weight": 0.1, "is_enabled": true, "min_value": 0, "max_value": 100, "normalization_method": "z_score", "update_frequency": "daily", "data_requirements": ["market_cap", "net_profit"], "created_time": "2025-07-29T11:11:02.719045", "updated_time": "2025-07-29T11:11:02.719045"}, "fund_pb_ratio": {"factor_id": "fund_pb_ratio", "factor_name": "PB_Ratio", "factor_name_cn": "市净率", "category": "FUNDAMENTAL", "factor_type": "VALUE", "description": "市净率，衡量估值水平", "calculation_method": "pb_ratio", "parameters": {"period": "latest", "max_pb": 20}, "weight": 0.08, "is_enabled": true, "min_value": 0, "max_value": 20, "normalization_method": "z_score", "update_frequency": "daily", "data_requirements": ["market_cap", "shareholders_equity"], "created_time": "2025-07-29T11:11:02.719046", "updated_time": "2025-07-29T11:11:02.719046"}, "fund_revenue_growth": {"factor_id": "fund_revenue_growth", "factor_name": "Revenue_Growth", "factor_name_cn": "营收增长率", "category": "FUNDAMENTAL", "factor_type": "GROWTH", "description": "营业收入增长率，衡量成长性", "calculation_method": "revenue_growth", "parameters": {"period": "yoy", "min_growth": -0.5, "max_growth": 2.0}, "weight": 0.1, "is_enabled": true, "min_value": -0.5, "max_value": 2.0, "normalization_method": "z_score", "update_frequency": "daily", "data_requirements": ["revenue"], "created_time": "2025-07-29T11:11:02.719047", "updated_time": "2025-07-29T11:11:02.719047"}, "market_momentum": {"factor_id": "market_momentum", "factor_name": "Price_Momentum", "factor_name_cn": "价格动量", "category": "MARKET", "factor_type": "MOMENTUM", "description": "价格动量因子，衡量价格趋势", "calculation_method": "price_momentum", "parameters": {"periods": [5, 10, 20], "weights": [0.5, 0.3, 0.2]}, "weight": 0.1, "is_enabled": true, "min_value": null, "max_value": null, "normalization_method": "z_score", "update_frequency": "daily", "data_requirements": ["close_price"], "created_time": "2025-07-29T11:11:02.719048", "updated_time": "2025-07-29T11:11:02.719048"}}