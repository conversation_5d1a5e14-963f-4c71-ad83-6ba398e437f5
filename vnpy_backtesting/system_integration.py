"""
VeighNa回测系统集成模块
整合所有回测组件，提供统一的接口
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, date, timedelta
import json

from .backtesting_engine import BacktestingEngine
from .multi_timeframe_strategy import MultiTimeframeStrategy
from .parameter_optimizer import ParameterOptimizer
from .performance_analyzer import PerformanceAnalyzer
from .risk_analyzer import RiskAnalyzer
from .report_generator import ReportGenerator

logger = logging.getLogger(__name__)

class VeighNaBacktestingSystem:
    """VeighNa回测系统集成类"""
    
    def __init__(self):
        """初始化回测系统"""
        self.engine = BacktestingEngine()
        self.optimizer = ParameterOptimizer()
        self.performance_analyzer = PerformanceAnalyzer()
        self.risk_analyzer = RiskAnalyzer()
        self.report_generator = ReportGenerator()
        
        # 系统状态
        self.is_initialized = False
        self.current_strategy = None
        self.last_backtest_results = None
        
        logger.info("🚀 VeighNa回测系统集成模块初始化完成")
    
    def run_complete_backtest(self,
                            symbols: List[str],
                            strategy_class: type,
                            strategy_params: Dict[str, Any],
                            start_date: date,
                            end_date: date,
                            initial_capital: float = 1000000.0,
                            timeframe: str = "1d") -> Dict[str, Any]:
        """
        运行完整的回测流程
        
        Args:
            symbols: 股票代码列表
            strategy_class: 策略类
            strategy_params: 策略参数
            start_date: 开始日期
            end_date: 结束日期
            initial_capital: 初始资金
            timeframe: 时间周期
            
        Returns:
            完整的回测结果
        """
        try:
            logger.info("🚀 开始完整回测流程...")
            logger.info(f"  股票数量: {len(symbols)}")
            logger.info(f"  策略类: {strategy_class.__name__}")
            logger.info(f"  回测期间: {start_date} ~ {end_date}")
            logger.info(f"  初始资金: {initial_capital:,.0f}")
            
            # 1. 配置回测引擎
            self.engine.set_parameters(
                start_date=start_date,
                end_date=end_date,
                initial_capital=initial_capital
            )
            
            # 2. 加载数据
            logger.info("📊 加载回测数据...")
            if not self.engine.load_data(symbols, timeframe):
                raise Exception("数据加载失败")
            
            # 3. 设置策略
            logger.info("⚙️ 设置回测策略...")
            if not self.engine.set_strategy(strategy_class, strategy_params):
                raise Exception("策略设置失败")
            
            # 4. 运行回测
            logger.info("🔄 运行回测...")
            if not self.engine.run_backtest():
                raise Exception("回测运行失败")
            
            # 5. 获取回测结果
            backtest_results = self.engine.get_results()
            if not backtest_results:
                raise Exception("获取回测结果失败")
            
            # 6. 绩效分析
            logger.info("📈 进行绩效分析...")
            performance_analysis = self.performance_analyzer.analyze_performance(backtest_results)
            
            # 7. 风险分析
            logger.info("⚠️ 进行风险分析...")
            risk_analysis = self.risk_analyzer.analyze_risk(backtest_results)
            
            # 8. 生成报告
            logger.info("📄 生成回测报告...")
            report = self.report_generator.generate_comprehensive_report(
                backtest_results, performance_analysis, risk_analysis, strategy_class.__name__
            )
            
            # 9. 整合结果
            complete_results = {
                'backtest_results': backtest_results,
                'performance_analysis': performance_analysis,
                'risk_analysis': risk_analysis,
                'report': report,
                'metadata': {
                    'symbols': symbols,
                    'strategy_name': strategy_class.__name__,
                    'strategy_params': strategy_params,
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'initial_capital': initial_capital,
                    'timeframe': timeframe,
                    'backtest_date': datetime.now().isoformat()
                }
            }
            
            self.last_backtest_results = complete_results
            
            logger.info("✅ 完整回测流程完成")
            self._log_backtest_summary(complete_results)
            
            return complete_results
            
        except Exception as e:
            logger.error(f"❌ 完整回测流程失败: {e}")
            return {}
    
    def optimize_strategy_parameters(self,
                                   symbols: List[str],
                                   strategy_class: type,
                                   parameter_ranges: Dict[str, tuple],
                                   start_date: date,
                                   end_date: date,
                                   optimization_method: str = "genetic",
                                   target_metric: str = "sharpe_ratio") -> Dict[str, Any]:
        """
        优化策略参数
        
        Args:
            symbols: 股票代码列表
            strategy_class: 策略类
            parameter_ranges: 参数范围
            start_date: 开始日期
            end_date: 结束日期
            optimization_method: 优化方法 (genetic/grid)
            target_metric: 目标指标
            
        Returns:
            优化结果
        """
        try:
            logger.info("🧬 开始参数优化...")
            
            # 配置回测引擎
            engine_config = {
                'start_date': start_date,
                'end_date': end_date,
                'symbols': symbols
            }
            
            # 定义目标函数
            def target_function(results):
                performance = self.performance_analyzer.analyze_performance(results)
                return performance.get(target_metric, 0)
            
            # 执行优化
            if optimization_method == "genetic":
                optimization_results = self.optimizer.genetic_algorithm_optimization(
                    strategy_class=strategy_class,
                    parameter_ranges=parameter_ranges,
                    target_function=target_function,
                    engine_config=engine_config
                )
            else:
                optimization_results = self.optimizer.grid_search_optimization(
                    strategy_class=strategy_class,
                    parameter_ranges=parameter_ranges,
                    target_function=target_function,
                    engine_config=engine_config
                )
            
            logger.info("✅ 参数优化完成")
            return optimization_results
            
        except Exception as e:
            logger.error(f"❌ 参数优化失败: {e}")
            return []
    
    def batch_backtest(self,
                      symbols_list: List[List[str]],
                      strategy_configs: List[Dict[str, Any]],
                      start_date: date,
                      end_date: date) -> List[Dict[str, Any]]:
        """
        批量回测
        
        Args:
            symbols_list: 股票组合列表
            strategy_configs: 策略配置列表
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            批量回测结果
        """
        try:
            logger.info(f"📊 开始批量回测: {len(symbols_list)}个组合, {len(strategy_configs)}个策略")
            
            batch_results = []
            
            for i, symbols in enumerate(symbols_list):
                for j, config in enumerate(strategy_configs):
                    logger.info(f"🔄 回测进度: 组合{i+1}/{len(symbols_list)}, 策略{j+1}/{len(strategy_configs)}")
                    
                    result = self.run_complete_backtest(
                        symbols=symbols,
                        strategy_class=config['strategy_class'],
                        strategy_params=config['strategy_params'],
                        start_date=start_date,
                        end_date=end_date,
                        initial_capital=config.get('initial_capital', 1000000.0),
                        timeframe=config.get('timeframe', '1d')
                    )
                    
                    if result:
                        result['batch_info'] = {
                            'symbols_index': i,
                            'strategy_index': j,
                            'symbols_count': len(symbols),
                            'strategy_name': config['strategy_class'].__name__
                        }
                        batch_results.append(result)
            
            logger.info(f"✅ 批量回测完成: {len(batch_results)}个结果")
            return batch_results
            
        except Exception as e:
            logger.error(f"❌ 批量回测失败: {e}")
            return []
    
    def save_results(self, results: Dict[str, Any], filename: str) -> bool:
        """保存回测结果"""
        try:
            # 保存JSON格式
            json_filename = f"{filename}.json"
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)
            
            # 保存报告
            if 'report' in results:
                self.report_generator.save_report_to_file(results['report'], filename)
            
            logger.info(f"✅ 结果已保存: {json_filename}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存结果失败: {e}")
            return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'is_initialized': self.is_initialized,
            'current_strategy': self.current_strategy.__name__ if self.current_strategy else None,
            'has_results': self.last_backtest_results is not None,
            'engine_status': 'ready',
            'components': {
                'engine': self.engine is not None,
                'optimizer': self.optimizer is not None,
                'performance_analyzer': self.performance_analyzer is not None,
                'risk_analyzer': self.risk_analyzer is not None,
                'report_generator': self.report_generator is not None
            }
        }
    
    def _log_backtest_summary(self, results: Dict[str, Any]) -> None:
        """记录回测摘要"""
        try:
            performance = results.get('performance_analysis', {})
            risk = results.get('risk_analysis', {})
            
            logger.info("📊 回测结果摘要:")
            logger.info(f"  总收益率: {performance.get('total_return', 0):.2%}")
            logger.info(f"  年化收益率: {performance.get('annual_return', 0):.2%}")
            logger.info(f"  最大回撤: {performance.get('max_drawdown', 0):.2%}")
            logger.info(f"  夏普比率: {performance.get('sharpe_ratio', 0):.2f}")
            logger.info(f"  风险评级: {risk.get('risk_rating', {}).get('risk_description', '未知')}")
            
        except Exception as e:
            logger.error(f"❌ 记录回测摘要失败: {e}")
