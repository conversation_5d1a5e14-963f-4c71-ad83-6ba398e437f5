-- SQLite兼容的数据库初始化脚本
-- 量化交易系统数据库结构

-- ============================================================================
-- 1. 基础数据表
-- ============================================================================

-- 股票基本信息表
CREATE TABLE IF NOT EXISTS stock_info (
    id TEXT PRIMARY KEY,
    symbol TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    industry TEXT,
    sector TEXT,
    market TEXT,
    list_date DATE,
    delist_date DATE,
    market_cap INTEGER,
    total_shares INTEGER,
    float_shares INTEGER,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- 2. 多时间周期行情数据表
-- ============================================================================

-- 1分钟K线数据表
CREATE TABLE IF NOT EXISTS minute_1_market (
    id TEXT PRIMARY KEY,
    symbol TEXT NOT NULL,
    trade_datetime TIMESTAMP NOT NULL,
    open_price DECIMAL(10, 3),
    high_price DECIMAL(10, 3),
    low_price DECIMAL(10, 3),
    close_price DECIMAL(10, 3),
    volume INTEGER,
    amount DECIMAL(20, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, trade_datetime)
);

-- 5分钟K线数据表
CREATE TABLE IF NOT EXISTS minute_5_market (
    id TEXT PRIMARY KEY,
    symbol TEXT NOT NULL,
    trade_datetime TIMESTAMP NOT NULL,
    open_price DECIMAL(10, 3),
    high_price DECIMAL(10, 3),
    low_price DECIMAL(10, 3),
    close_price DECIMAL(10, 3),
    volume INTEGER,
    amount DECIMAL(20, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, trade_datetime)
);

-- 15分钟K线数据表
CREATE TABLE IF NOT EXISTS minute_15_market (
    id TEXT PRIMARY KEY,
    symbol TEXT NOT NULL,
    trade_datetime TIMESTAMP NOT NULL,
    open_price DECIMAL(10, 3),
    high_price DECIMAL(10, 3),
    low_price DECIMAL(10, 3),
    close_price DECIMAL(10, 3),
    volume INTEGER,
    amount DECIMAL(20, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, trade_datetime)
);

-- 1小时K线数据表
CREATE TABLE IF NOT EXISTS hour_1_market (
    id TEXT PRIMARY KEY,
    symbol TEXT NOT NULL,
    trade_datetime TIMESTAMP NOT NULL,
    open_price DECIMAL(10, 3),
    high_price DECIMAL(10, 3),
    low_price DECIMAL(10, 3),
    close_price DECIMAL(10, 3),
    volume INTEGER,
    amount DECIMAL(20, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, trade_datetime)
);

-- 4小时K线数据表
CREATE TABLE IF NOT EXISTS hour_4_market (
    id TEXT PRIMARY KEY,
    symbol TEXT NOT NULL,
    trade_datetime TIMESTAMP NOT NULL,
    open_price DECIMAL(10, 3),
    high_price DECIMAL(10, 3),
    low_price DECIMAL(10, 3),
    close_price DECIMAL(10, 3),
    volume INTEGER,
    amount DECIMAL(20, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, trade_datetime)
);

-- 日线数据表
CREATE TABLE IF NOT EXISTS daily_market (
    id TEXT PRIMARY KEY,
    symbol TEXT NOT NULL,
    trade_date DATE NOT NULL,
    open_price DECIMAL(10, 3),
    high_price DECIMAL(10, 3),
    low_price DECIMAL(10, 3),
    close_price DECIMAL(10, 3),
    volume INTEGER,
    amount DECIMAL(20, 2),
    turnover_rate DECIMAL(8, 4),
    pe_ratio DECIMAL(8, 2),
    pb_ratio DECIMAL(8, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, trade_date)
);

-- ============================================================================
-- 3. 因子管理表
-- ============================================================================

-- 因子配置表
CREATE TABLE IF NOT EXISTS factor_config (
    id TEXT PRIMARY KEY,
    factor_id TEXT UNIQUE NOT NULL,
    factor_name TEXT NOT NULL,
    factor_category TEXT NOT NULL,
    factor_type TEXT NOT NULL,
    weight DECIMAL(5,4) NOT NULL DEFAULT 0.0000,
    description TEXT,
    parameters TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 扩展因子配置表
CREATE TABLE IF NOT EXISTS factor_config_extended (
    factor_id TEXT PRIMARY KEY,
    factor_name TEXT NOT NULL,
    factor_name_cn TEXT NOT NULL,
    factor_category TEXT NOT NULL,
    factor_type TEXT NOT NULL,
    description TEXT,
    calculation_method TEXT NOT NULL,
    parameters TEXT,
    weight DECIMAL(5,4) NOT NULL DEFAULT 0.0000,
    is_enabled BOOLEAN DEFAULT 1,
    min_value REAL,
    max_value REAL,
    normalization_method TEXT DEFAULT 'z_score',
    update_frequency TEXT DEFAULT 'daily',
    data_requirements TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 因子值存储表
CREATE TABLE IF NOT EXISTS factor_values (
    id TEXT PRIMARY KEY,
    symbol TEXT NOT NULL,
    factor_id TEXT NOT NULL,
    trade_date DATE NOT NULL,
    factor_value REAL,
    normalized_value REAL,
    percentile_rank REAL,
    z_score REAL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, factor_id, trade_date)
);

-- ============================================================================
-- 4. 交易信号表
-- ============================================================================

-- 股票评分表
CREATE TABLE IF NOT EXISTS stock_scores (
    id TEXT PRIMARY KEY,
    symbol TEXT NOT NULL,
    calculation_date DATE NOT NULL,
    technical_score DECIMAL(5,2),
    fundamental_score DECIMAL(5,2),
    market_score DECIMAL(5,2),
    total_score DECIMAL(5,2),
    rank_score INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, calculation_date)
);

-- 交易信号表
CREATE TABLE IF NOT EXISTS trading_signals (
    id TEXT PRIMARY KEY,
    symbol TEXT NOT NULL,
    signal_type TEXT NOT NULL,
    signal_strength DECIMAL(3,2),
    signal_price DECIMAL(10,3),
    signal_time TIMESTAMP NOT NULL,
    strategy_name TEXT,
    parameters TEXT,
    is_executed BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- 5. 投资组合管理表
-- ============================================================================

-- 投资组合表
CREATE TABLE IF NOT EXISTS portfolios (
    id TEXT PRIMARY KEY,
    portfolio_name TEXT NOT NULL,
    portfolio_type TEXT,
    initial_capital DECIMAL(15,2),
    current_value DECIMAL(15,2),
    cash_balance DECIMAL(15,2),
    status TEXT DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 投资组合持仓表
CREATE TABLE IF NOT EXISTS portfolio_holdings (
    id TEXT PRIMARY KEY,
    portfolio_id TEXT NOT NULL,
    symbol TEXT NOT NULL,
    quantity INTEGER,
    avg_cost DECIMAL(10,3),
    current_price DECIMAL(10,3),
    market_value DECIMAL(15,2),
    weight DECIMAL(5,4),
    unrealized_pnl DECIMAL(15,2),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(portfolio_id, symbol)
);

-- 交易订单表
CREATE TABLE IF NOT EXISTS trading_orders (
    id TEXT PRIMARY KEY,
    portfolio_id TEXT,
    symbol TEXT NOT NULL,
    order_type TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    price DECIMAL(10,3),
    status TEXT DEFAULT 'PENDING',
    filled_quantity INTEGER DEFAULT 0,
    avg_fill_price DECIMAL(10,3),
    commission DECIMAL(10,2),
    order_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fill_time TIMESTAMP
);

-- ============================================================================
-- 6. VeighNa回测系统表
-- ============================================================================

-- 回测策略表
CREATE TABLE IF NOT EXISTS backtest_strategies (
    id TEXT PRIMARY KEY,
    strategy_name TEXT NOT NULL,
    strategy_class TEXT NOT NULL,
    strategy_type TEXT,
    parameters TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 回测结果表
CREATE TABLE IF NOT EXISTS backtest_results (
    id TEXT PRIMARY KEY,
    strategy_id TEXT NOT NULL,
    symbols TEXT,
    start_date DATE,
    end_date DATE,
    initial_capital DECIMAL(15,2),
    final_capital DECIMAL(15,2),
    total_return DECIMAL(8,4),
    annual_return DECIMAL(8,4),
    max_drawdown DECIMAL(8,4),
    sharpe_ratio DECIMAL(6,3),
    win_rate DECIMAL(5,4),
    total_trades INTEGER,
    backtest_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- 7. 系统管理表
-- ============================================================================

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id TEXT PRIMARY KEY,
    config_key TEXT UNIQUE NOT NULL,
    config_value TEXT,
    config_type TEXT,
    description TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 系统日志表
CREATE TABLE IF NOT EXISTS system_logs (
    id TEXT PRIMARY KEY,
    log_level TEXT NOT NULL,
    logger_name TEXT,
    message TEXT,
    module_name TEXT,
    function_name TEXT,
    line_number INTEGER,
    log_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- 8. 创建索引
-- ============================================================================

-- 股票信息索引
CREATE INDEX IF NOT EXISTS idx_stock_info_symbol ON stock_info(symbol);
CREATE INDEX IF NOT EXISTS idx_stock_info_market ON stock_info(market);

-- 行情数据索引
CREATE INDEX IF NOT EXISTS idx_minute_1_symbol_time ON minute_1_market(symbol, trade_datetime);
CREATE INDEX IF NOT EXISTS idx_minute_5_symbol_time ON minute_5_market(symbol, trade_datetime);
CREATE INDEX IF NOT EXISTS idx_minute_15_symbol_time ON minute_15_market(symbol, trade_datetime);
CREATE INDEX IF NOT EXISTS idx_hour_1_symbol_time ON hour_1_market(symbol, trade_datetime);
CREATE INDEX IF NOT EXISTS idx_hour_4_symbol_time ON hour_4_market(symbol, trade_datetime);
CREATE INDEX IF NOT EXISTS idx_daily_symbol_date ON daily_market(symbol, trade_date);

-- 因子相关索引
CREATE INDEX IF NOT EXISTS idx_factor_config_category ON factor_config(factor_category);
CREATE INDEX IF NOT EXISTS idx_factor_values_symbol_date ON factor_values(symbol, trade_date);
CREATE INDEX IF NOT EXISTS idx_factor_values_factor_date ON factor_values(factor_id, trade_date);

-- 交易信号索引
CREATE INDEX IF NOT EXISTS idx_stock_scores_symbol_date ON stock_scores(symbol, calculation_date);
CREATE INDEX IF NOT EXISTS idx_trading_signals_symbol_time ON trading_signals(symbol, signal_time);

-- 投资组合索引
CREATE INDEX IF NOT EXISTS idx_portfolio_holdings_portfolio ON portfolio_holdings(portfolio_id);
CREATE INDEX IF NOT EXISTS idx_trading_orders_portfolio ON trading_orders(portfolio_id);

-- 系统管理索引
CREATE INDEX IF NOT EXISTS idx_system_config_key ON system_config(config_key);
CREATE INDEX IF NOT EXISTS idx_system_logs_timestamp ON system_logs(log_timestamp);
