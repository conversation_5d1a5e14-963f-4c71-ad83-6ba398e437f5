"""
交易执行系统集成模块
整合所有交易执行组件，提供统一的接口
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, date
from enum import Enum
import uuid

logger = logging.getLogger(__name__)

class OrderType(Enum):
    """订单类型"""
    BUY = "BUY"
    SELL = "SELL"

class OrderStatus(Enum):
    """订单状态"""
    PENDING = "PENDING"
    PARTIAL = "PARTIAL"
    FILLED = "FILLED"
    CANCELLED = "CANCELLED"

class Order:
    """订单类"""
    
    def __init__(self, symbol: str, order_type: OrderType, quantity: int, price: float = None):
        self.id = str(uuid.uuid4())
        self.symbol = symbol
        self.order_type = order_type
        self.quantity = quantity
        self.price = price
        self.status = OrderStatus.PENDING
        self.filled_quantity = 0
        self.avg_fill_price = 0.0
        self.commission = 0.0
        self.order_time = datetime.now()
        self.fill_time = None
        self.order_reason = ""

class Trade:
    """交易记录类"""
    
    def __init__(self, symbol: str, quantity: int, price: float, trade_type: str):
        self.id = str(uuid.uuid4())
        self.symbol = symbol
        self.quantity = quantity
        self.price = price
        self.trade_type = trade_type
        self.trade_time = datetime.now()
        self.commission = 0.0

class Position:
    """持仓类"""
    
    def __init__(self, symbol: str):
        self.symbol = symbol
        self.quantity = 0
        self.avg_cost = 0.0
        self.current_price = 0.0
        self.market_value = 0.0
        self.unrealized_pnl = 0.0
        self.realized_pnl = 0.0

class TradingExecutionSystem:
    """交易执行系统集成类"""
    
    def __init__(self, initial_capital: float = 1000000.0):
        """初始化交易执行系统"""
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.cash_balance = initial_capital
        
        # 交易配置
        self.commission_rate = 0.0003  # 手续费率0.03%
        self.slippage_rate = 0.001     # 滑点0.1%
        self.min_commission = 5.0      # 最小手续费5元
        
        # 风险控制参数
        self.max_position_weight = 0.10  # 单股最大权重10%
        self.max_total_position = 0.80   # 总仓位限制80%
        self.max_drawdown_limit = 0.15   # 最大回撤限制15%
        
        # 数据存储
        self.orders: Dict[str, Order] = {}
        self.trades: List[Trade] = []
        self.positions: Dict[str, Position] = {}
        self.daily_pnl: List[Dict[str, Any]] = []
        
        # 系统状态
        self.is_trading = False
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        
        logger.info("⚡ 交易执行系统初始化完成")
        logger.info(f"  初始资金: {initial_capital:,.0f}")
        logger.info(f"  手续费率: {self.commission_rate:.4f}")
        logger.info(f"  滑点率: {self.slippage_rate:.4f}")
    
    def start_trading(self) -> bool:
        """开始交易"""
        try:
            self.is_trading = True
            logger.info("🚀 交易系统启动")
            return True
        except Exception as e:
            logger.error(f"❌ 启动交易系统失败: {e}")
            return False
    
    def stop_trading(self) -> bool:
        """停止交易"""
        try:
            self.is_trading = False
            logger.info("⏹️ 交易系统停止")
            return True
        except Exception as e:
            logger.error(f"❌ 停止交易系统失败: {e}")
            return False
    
    def submit_order(self, symbol: str, order_type: OrderType, quantity: int, 
                    price: float = None, reason: str = "") -> Optional[str]:
        """
        提交订单
        
        Args:
            symbol: 股票代码
            order_type: 订单类型
            quantity: 数量
            price: 价格（None为市价单）
            reason: 下单原因
            
        Returns:
            订单ID
        """
        try:
            if not self.is_trading:
                logger.warning("⚠️ 交易系统未启动")
                return None
            
            # 风险检查
            if not self._risk_check(symbol, order_type, quantity, price):
                logger.warning(f"⚠️ 风险检查未通过: {symbol} {order_type.value} {quantity}")
                return None
            
            # 创建订单
            order = Order(symbol, order_type, quantity, price)
            order.order_reason = reason
            
            self.orders[order.id] = order
            
            logger.info(f"📝 订单提交: {order.id} - {symbol} {order_type.value} {quantity}@{price}")
            
            # 模拟执行订单
            self._execute_order(order.id)
            
            return order.id
            
        except Exception as e:
            logger.error(f"❌ 提交订单失败: {symbol} - {e}")
            return None
    
    def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        try:
            if order_id not in self.orders:
                logger.warning(f"⚠️ 订单不存在: {order_id}")
                return False
            
            order = self.orders[order_id]
            if order.status in [OrderStatus.FILLED, OrderStatus.CANCELLED]:
                logger.warning(f"⚠️ 订单状态不允许取消: {order.status}")
                return False
            
            order.status = OrderStatus.CANCELLED
            logger.info(f"❌ 订单已取消: {order_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 取消订单失败: {order_id} - {e}")
            return False
    
    def _execute_order(self, order_id: str) -> bool:
        """执行订单（模拟）"""
        try:
            order = self.orders[order_id]
            
            # 获取当前价格（模拟）
            current_price = self._get_current_price(order.symbol)
            if current_price is None:
                logger.error(f"❌ 无法获取价格: {order.symbol}")
                return False
            
            # 计算执行价格（考虑滑点）
            if order.order_type == OrderType.BUY:
                execution_price = current_price * (1 + self.slippage_rate)
            else:
                execution_price = current_price * (1 - self.slippage_rate)
            
            # 计算手续费
            trade_value = order.quantity * execution_price
            commission = max(trade_value * self.commission_rate, self.min_commission)
            
            # 检查资金是否充足
            if order.order_type == OrderType.BUY:
                required_cash = trade_value + commission
                if required_cash > self.cash_balance:
                    logger.warning(f"⚠️ 资金不足: 需要{required_cash:.2f}, 可用{self.cash_balance:.2f}")
                    return False
            
            # 执行交易
            order.status = OrderStatus.FILLED
            order.filled_quantity = order.quantity
            order.avg_fill_price = execution_price
            order.commission = commission
            order.fill_time = datetime.now()
            
            # 创建交易记录
            trade = Trade(order.symbol, order.quantity, execution_price, order.order_type.value)
            trade.commission = commission
            self.trades.append(trade)
            
            # 更新持仓
            self._update_position(order.symbol, order.order_type, order.quantity, execution_price)
            
            # 更新资金
            if order.order_type == OrderType.BUY:
                self.cash_balance -= (trade_value + commission)
            else:
                self.cash_balance += (trade_value - commission)
            
            self.total_trades += 1
            
            logger.info(f"✅ 订单执行完成: {order_id} - {execution_price:.3f}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 执行订单失败: {order_id} - {e}")
            return False
    
    def _update_position(self, symbol: str, order_type: OrderType, quantity: int, price: float):
        """更新持仓"""
        try:
            if symbol not in self.positions:
                self.positions[symbol] = Position(symbol)
            
            position = self.positions[symbol]
            
            if order_type == OrderType.BUY:
                # 买入
                total_cost = position.quantity * position.avg_cost + quantity * price
                position.quantity += quantity
                position.avg_cost = total_cost / position.quantity if position.quantity > 0 else 0
            else:
                # 卖出
                if position.quantity >= quantity:
                    # 计算已实现盈亏
                    realized_pnl = (price - position.avg_cost) * quantity
                    position.realized_pnl += realized_pnl
                    position.quantity -= quantity
                    
                    # 更新交易统计
                    if realized_pnl > 0:
                        self.winning_trades += 1
                    else:
                        self.losing_trades += 1
                else:
                    logger.warning(f"⚠️ 持仓不足: {symbol} 持仓{position.quantity} 卖出{quantity}")
            
            # 更新市值和浮动盈亏
            position.current_price = price
            position.market_value = position.quantity * price
            position.unrealized_pnl = (price - position.avg_cost) * position.quantity
            
        except Exception as e:
            logger.error(f"❌ 更新持仓失败: {symbol} - {e}")
    
    def _get_current_price(self, symbol: str) -> Optional[float]:
        """获取当前价格（模拟）"""
        try:
            # 这里应该连接实际的数据源
            # 现在使用模拟价格
            import random
            base_price = 10.0 + hash(symbol) % 100
            return base_price * (0.95 + random.random() * 0.1)
        except Exception as e:
            logger.error(f"❌ 获取价格失败: {symbol} - {e}")
            return None
    
    def _risk_check(self, symbol: str, order_type: OrderType, quantity: int, price: float) -> bool:
        """风险检查"""
        try:
            # 获取当前价格
            current_price = price or self._get_current_price(symbol)
            if current_price is None:
                return False
            
            if order_type == OrderType.BUY:
                # 买入风险检查
                trade_value = quantity * current_price
                
                # 检查单股权重
                position_weight = trade_value / self.current_capital
                if position_weight > self.max_position_weight:
                    logger.warning(f"⚠️ 单股权重超限: {position_weight:.2%} > {self.max_position_weight:.2%}")
                    return False
                
                # 检查总仓位
                total_position_value = sum(pos.market_value for pos in self.positions.values())
                total_position_weight = (total_position_value + trade_value) / self.current_capital
                if total_position_weight > self.max_total_position:
                    logger.warning(f"⚠️ 总仓位超限: {total_position_weight:.2%} > {self.max_total_position:.2%}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 风险检查失败: {symbol} - {e}")
            return False
    
    def get_portfolio_status(self) -> Dict[str, Any]:
        """获取投资组合状态"""
        try:
            total_market_value = sum(pos.market_value for pos in self.positions.values())
            total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
            total_realized_pnl = sum(pos.realized_pnl for pos in self.positions.values())
            
            current_value = self.cash_balance + total_market_value
            total_return = (current_value - self.initial_capital) / self.initial_capital
            
            return {
                'initial_capital': self.initial_capital,
                'current_value': current_value,
                'cash_balance': self.cash_balance,
                'total_market_value': total_market_value,
                'total_return': total_return,
                'total_unrealized_pnl': total_unrealized_pnl,
                'total_realized_pnl': total_realized_pnl,
                'position_count': len([pos for pos in self.positions.values() if pos.quantity > 0]),
                'total_trades': self.total_trades,
                'winning_trades': self.winning_trades,
                'losing_trades': self.losing_trades,
                'win_rate': self.winning_trades / max(self.total_trades, 1),
                'is_trading': self.is_trading
            }
            
        except Exception as e:
            logger.error(f"❌ 获取投资组合状态失败: {e}")
            return {}
    
    def get_positions(self) -> Dict[str, Dict[str, Any]]:
        """获取持仓信息"""
        try:
            positions_data = {}
            for symbol, position in self.positions.items():
                if position.quantity > 0:
                    positions_data[symbol] = {
                        'symbol': symbol,
                        'quantity': position.quantity,
                        'avg_cost': position.avg_cost,
                        'current_price': position.current_price,
                        'market_value': position.market_value,
                        'unrealized_pnl': position.unrealized_pnl,
                        'realized_pnl': position.realized_pnl,
                        'weight': position.market_value / self.current_capital if self.current_capital > 0 else 0
                    }
            return positions_data
        except Exception as e:
            logger.error(f"❌ 获取持仓信息失败: {e}")
            return {}
    
    def get_orders(self, status: OrderStatus = None) -> List[Dict[str, Any]]:
        """获取订单信息"""
        try:
            orders_data = []
            for order in self.orders.values():
                if status is None or order.status == status:
                    orders_data.append({
                        'id': order.id,
                        'symbol': order.symbol,
                        'order_type': order.order_type.value,
                        'quantity': order.quantity,
                        'price': order.price,
                        'status': order.status.value,
                        'filled_quantity': order.filled_quantity,
                        'avg_fill_price': order.avg_fill_price,
                        'commission': order.commission,
                        'order_time': order.order_time.isoformat(),
                        'fill_time': order.fill_time.isoformat() if order.fill_time else None,
                        'order_reason': order.order_reason
                    })
            return orders_data
        except Exception as e:
            logger.error(f"❌ 获取订单信息失败: {e}")
            return []
    
    def get_trades(self) -> List[Dict[str, Any]]:
        """获取交易记录"""
        try:
            trades_data = []
            for trade in self.trades:
                trades_data.append({
                    'id': trade.id,
                    'symbol': trade.symbol,
                    'quantity': trade.quantity,
                    'price': trade.price,
                    'trade_type': trade.trade_type,
                    'trade_time': trade.trade_time.isoformat(),
                    'commission': trade.commission,
                    'trade_value': trade.quantity * trade.price
                })
            return trades_data
        except Exception as e:
            logger.error(f"❌ 获取交易记录失败: {e}")
            return []
