"""
交易执行层
提供模拟交易引擎、订单管理系统、滑点控制、成交回报处理等功能
"""

from .trading_system_integration import TradingExecutionSystem, Order, Trade, Position, OrderType, OrderStatus

__version__ = "2.0.0"

__all__ = [
    'TradingExecutionSystem',
    'Order',
    'Trade',
    'Position',
    'OrderType',
    'OrderStatus'
]

# 其他模块可以按需导入
# from .trading_engine.simulation_engine import SimulationTradingEngine
# from .order_manager.order_manager import OrderManager
