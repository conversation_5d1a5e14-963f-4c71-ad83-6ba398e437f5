#!/usr/bin/env python3
"""
量化交易系统启动器
统一的系统启动入口，整合所有功能模块
"""

import sys
import logging
import argparse
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

# 导入各个系统模块
from vnpy_backtesting import VeighNaBacktestingSystem
from factor_management import FactorManagementSystem
from visualization import VisualizationSystem
from trading_execution import TradingExecutionSystem
from portfolio_management import PortfolioManagementSystem
from system_management import SystemManagementIntegration

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/system.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class QuantitativeTradingSystemLauncher:
    """量化交易系统启动器"""
    
    def __init__(self):
        """初始化系统启动器"""
        self.systems = {}
        self.is_initialized = False
        
        logger.info("🚀 量化交易系统启动器初始化")
    
    def initialize_systems(self) -> bool:
        """初始化所有系统模块"""
        try:
            logger.info("🔧 开始初始化系统模块...")
            
            # 1. 系统管理模块
            logger.info("  初始化系统管理模块...")
            self.systems['system_management'] = SystemManagementIntegration()
            
            # 2. 因子管理系统
            logger.info("  初始化因子管理系统...")
            self.systems['factor_management'] = FactorManagementSystem()
            if not self.systems['factor_management'].initialize_system():
                logger.error("❌ 因子管理系统初始化失败")
                return False
            
            # 3. VeighNa回测系统
            logger.info("  初始化VeighNa回测系统...")
            self.systems['vnpy_backtesting'] = VeighNaBacktestingSystem()
            
            # 4. 投资组合管理系统
            logger.info("  初始化投资组合管理系统...")
            self.systems['portfolio_management'] = PortfolioManagementSystem()
            
            # 5. 交易执行系统
            logger.info("  初始化交易执行系统...")
            self.systems['trading_execution'] = TradingExecutionSystem()
            
            # 6. 可视化系统
            logger.info("  初始化可视化系统...")
            self.systems['visualization'] = VisualizationSystem()
            
            self.is_initialized = True
            logger.info("✅ 所有系统模块初始化完成")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 系统初始化失败: {e}")
            return False
    
    def run_backtest_demo(self):
        """运行回测演示"""
        try:
            logger.info("📊 开始回测演示...")
            
            if not self.is_initialized:
                logger.error("❌ 系统未初始化")
                return
            
            # 模拟股票列表
            symbols = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ']
            
            # 模拟策略类（这里需要实际的策略实现）
            from vnpy_backtesting.multi_timeframe_strategy import MultiTimeframeStrategy
            
            # 策略参数
            strategy_params = {
                'rsi_period': 14,
                'ma_period': 20,
                'volume_threshold': 1.5
            }
            
            # 回测期间
            from datetime import date, timedelta
            end_date = date.today()
            start_date = end_date - timedelta(days=365)
            
            # 运行完整回测
            vnpy_system = self.systems['vnpy_backtesting']
            results = vnpy_system.run_complete_backtest(
                symbols=symbols,
                strategy_class=MultiTimeframeStrategy,
                strategy_params=strategy_params,
                start_date=start_date,
                end_date=end_date,
                initial_capital=1000000.0
            )
            
            if results:
                logger.info("✅ 回测演示完成")
                self._display_backtest_results(results)
            else:
                logger.error("❌ 回测演示失败")
                
        except Exception as e:
            logger.error(f"❌ 回测演示异常: {e}")
    
    def run_factor_analysis_demo(self):
        """运行因子分析演示"""
        try:
            logger.info("🔧 开始因子分析演示...")
            
            if not self.is_initialized:
                logger.error("❌ 系统未初始化")
                return
            
            factor_system = self.systems['factor_management']
            
            # 获取所有因子
            factors = factor_system.get_all_factors()
            logger.info(f"  加载因子数量: {len(factors)}")
            
            # 模拟股票列表
            symbols = ['000001.SZ', '000002.SZ', '600000.SH']
            
            # 计算因子值
            factor_values = factor_system.calculate_factors(symbols)
            
            if factor_values:
                logger.info("✅ 因子分析演示完成")
                logger.info(f"  计算结果: {len(factor_values)}只股票")
            else:
                logger.error("❌ 因子分析演示失败")
                
        except Exception as e:
            logger.error(f"❌ 因子分析演示异常: {e}")
    
    def run_portfolio_demo(self):
        """运行投资组合演示"""
        try:
            logger.info("💼 开始投资组合演示...")
            
            if not self.is_initialized:
                logger.error("❌ 系统未初始化")
                return
            
            portfolio_system = self.systems['portfolio_management']
            
            # 模拟股票评分
            stock_scores = {
                '000001.SZ': 85.5,
                '000002.SZ': 78.2,
                '600000.SH': 82.1,
                '600036.SH': 75.8,
                '000858.SZ': 88.3
            }
            
            # 模拟股票数据
            stock_data = {}
            for symbol in stock_scores:
                stock_data[symbol] = {
                    'market_cap': 50e9,
                    'avg_volume': 5e6,
                    'price': 15.0,
                    'sector': 'Finance',
                    'score': stock_scores[symbol],
                    'volatility': 0.25,
                    'expected_return': 0.12
                }
            
            # 构建投资组合
            from portfolio_management import WeightMethod
            portfolio = portfolio_system.construct_portfolio(
                stock_scores=stock_scores,
                stock_data=stock_data,
                weight_method=WeightMethod.SCORE_BASED,
                target_stocks=5
            )
            
            if portfolio:
                logger.info("✅ 投资组合演示完成")
                logger.info(f"  组合股票数: {len(portfolio)}")
            else:
                logger.error("❌ 投资组合演示失败")
                
        except Exception as e:
            logger.error(f"❌ 投资组合演示异常: {e}")
    
    def run_trading_demo(self):
        """运行交易演示"""
        try:
            logger.info("⚡ 开始交易演示...")
            
            if not self.is_initialized:
                logger.error("❌ 系统未初始化")
                return
            
            trading_system = self.systems['trading_execution']
            
            # 启动交易系统
            if not trading_system.start_trading():
                logger.error("❌ 启动交易系统失败")
                return
            
            # 提交买入订单
            from trading_execution import OrderType
            order_id = trading_system.submit_order(
                symbol='000001.SZ',
                order_type=OrderType.BUY,
                quantity=1000,
                reason='演示买入'
            )
            
            if order_id:
                logger.info(f"✅ 订单提交成功: {order_id}")
                
                # 获取投资组合状态
                status = trading_system.get_portfolio_status()
                logger.info(f"  当前资金: {status.get('current_value', 0):,.0f}")
                logger.info(f"  持仓数量: {status.get('position_count', 0)}")
            else:
                logger.error("❌ 订单提交失败")
            
            # 停止交易系统
            trading_system.stop_trading()
            
        except Exception as e:
            logger.error(f"❌ 交易演示异常: {e}")
    
    def _display_backtest_results(self, results: dict):
        """显示回测结果"""
        try:
            performance = results.get('performance_analysis', {})
            risk = results.get('risk_analysis', {})
            
            logger.info("📊 回测结果摘要:")
            logger.info(f"  总收益率: {performance.get('total_return', 0):.2%}")
            logger.info(f"  年化收益率: {performance.get('annual_return', 0):.2%}")
            logger.info(f"  最大回撤: {performance.get('max_drawdown', 0):.2%}")
            logger.info(f"  夏普比率: {performance.get('sharpe_ratio', 0):.2f}")
            logger.info(f"  胜率: {performance.get('win_rate', 0):.2%}")
            
        except Exception as e:
            logger.error(f"❌ 显示回测结果失败: {e}")
    
    def get_system_status(self) -> dict:
        """获取系统状态"""
        try:
            status = {
                'launcher': {
                    'is_initialized': self.is_initialized,
                    'systems_count': len(self.systems),
                    'startup_time': datetime.now().isoformat()
                },
                'systems': {}
            }
            
            for name, system in self.systems.items():
                if hasattr(system, 'get_system_status'):
                    status['systems'][name] = system.get_system_status()
                else:
                    status['systems'][name] = {'status': 'available'}
            
            return status
            
        except Exception as e:
            logger.error(f"❌ 获取系统状态失败: {e}")
            return {'error': str(e)}
    
    def shutdown(self):
        """关闭系统"""
        try:
            logger.info("🛑 开始关闭系统...")
            
            # 停止交易系统
            if 'trading_execution' in self.systems:
                self.systems['trading_execution'].stop_trading()
            
            # 清理资源
            self.systems.clear()
            self.is_initialized = False
            
            logger.info("✅ 系统关闭完成")
            
        except Exception as e:
            logger.error(f"❌ 系统关闭异常: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='量化交易系统启动器')
    parser.add_argument('--demo', choices=['backtest', 'factor', 'portfolio', 'trading', 'all'], 
                       help='运行演示模式')
    parser.add_argument('--status', action='store_true', help='显示系统状态')
    
    args = parser.parse_args()
    
    # 创建启动器
    launcher = QuantitativeTradingSystemLauncher()
    
    try:
        # 初始化系统
        if not launcher.initialize_systems():
            logger.error("❌ 系统初始化失败")
            return 1
        
        # 运行演示
        if args.demo:
            if args.demo == 'backtest' or args.demo == 'all':
                launcher.run_backtest_demo()
            
            if args.demo == 'factor' or args.demo == 'all':
                launcher.run_factor_analysis_demo()
            
            if args.demo == 'portfolio' or args.demo == 'all':
                launcher.run_portfolio_demo()
            
            if args.demo == 'trading' or args.demo == 'all':
                launcher.run_trading_demo()
        
        # 显示系统状态
        if args.status:
            status = launcher.get_system_status()
            print("\n" + "="*50)
            print("系统状态报告")
            print("="*50)
            print(f"初始化状态: {status['launcher']['is_initialized']}")
            print(f"系统模块数: {status['launcher']['systems_count']}")
            print(f"启动时间: {status['launcher']['startup_time']}")
            print("\n系统模块状态:")
            for name, sys_status in status['systems'].items():
                print(f"  {name}: {'✅' if sys_status else '❌'}")
        
        # 如果没有指定参数，显示帮助信息
        if not args.demo and not args.status:
            print("\n🚀 量化交易系统已启动")
            print("使用 --help 查看可用选项")
            print("使用 --demo all 运行所有演示")
            print("使用 --status 查看系统状态")
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("👋 用户中断，正在关闭系统...")
        return 0
    except Exception as e:
        logger.error(f"❌ 系统运行异常: {e}")
        return 1
    finally:
        launcher.shutdown()

if __name__ == '__main__':
    sys.exit(main())
